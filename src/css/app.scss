// app global css in SCSS form
//@use "sass:color";

.q-icon {
  cursor: pointer;
}

::selection {
  background: change-color($primary, $lightness: 75%, $saturation: 100%);
}

body {
  background-image: -webkit-linear-gradient(
    left,
    rgb(245, 243, 226),
    rgb(244, 240, 224)
  );
  background-image: url('/images/gradient.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-attachment: fixed;
}
#title {
  font-size: 20px;
  line-height: 34px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  line-height: normal;
  font-weight: bold;
}

h1 {
  font-size: 2em;
  margin-bottom: 0.67em;
}

h2 {
  font-size: 1.5em;
  margin-bottom: 0.83em;
}

h3 {
  font-size: 1.17em;
  margin-bottom: 1em;
}

h4 {
  font-size: 1em;
  margin-bottom: 1.33em;
}

h5 {
  font-size: 0.83em;
  margin-bottom: 1.67em;
}

h6 {
  font-size: 0.67em;
  margin-bottom: 2.33em;
}

.book-chapters-nav {
  .q-item.disabled {
    background-color: #fff !important;
    opacity: 1 !important;
  }
  .selected-chapter-nav .q-item--clickable {
    background-color: #03588c;
    color: #fff;
    border-radius: 3px;
    .q-item__label,
    .q-expansion-item__toggle-icon,
    .q-item__section--side {
      color: #fff;
    }
  }
  .q-expansion-item__toggle-icon {
    min-height: 1.4em;
    color: #000;
  }
}

.review-mode .comment-highlight {
  background: rgba($yellow, 0.33) !important;
}

.review-mode .comment-highlight.selected {
  background: $yellow !important;
}

.review-mode .comment-highlight.resolved {
  background: none !important;
}

.fixed-drawer-header {
  position: absolute;
  top: 50px;
  left: 0;
  width: 100%;
  padding: 24px;
  height: 104px;
  align-items: center;
}

.footnote-mode .footnote {
  border-bottom: 2px solid $primary;
}

.footnote {
  pointer-events: none;
}

.footnote:after {
  content: ' x';
  color: $primary !important;
  position: relative;
  top: -6px;
}

.footnote sup {
  color: $primary !important;
}
.chapters-list {
  .q-item__label {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    color: #080808;
  }
}

.rounded-borders {
  overflow: hidden;
}

.q-popup-edit__buttons {
  justify-content: flex-end;
}

.q-popup-edit__buttons .q-btn {
  background: #efefef;
}

thead tr th {
  position: sticky;
  z-index: 1;
  background: white;
}

thead tr:first-child th {
  top: 0;
  background: white;
}

/* For WebKit browsers */
::-webkit-scrollbar {
  height: 12px;
  width: 14px;
  background: $primary;
  opacity: 0.2;
  z-index: 12;
  overflow: visible;
}

::-webkit-scrollbar-thumb {
  width: 10px;
  background-color: $primary;
  border-radius: 10px;
  z-index: 12;
  border: 4px solid rgba(0, 0, 0, 0);
  background-clip: padding-box;
  -webkit-transition: background-color 0.28s ease-in-out;
  transition: background-color 0.28s ease-in-out;
  margin: 4px;
  min-height: 32px;
  min-width: 32px;
}

::-webkit-scrollbar-thumb:hover {
  background: #00b4ff;
}

/* For Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: $primary transparent; /* Replace with your primary color variable */
}

/* For Internet Explorer, Edge (Legacy) */
* {
  -ms-overflow-style: -ms-autohiding-scrollbar;
}

/* General styling for other browsers */
.scrollbar {
  scrollbar-width: thin;
  scrollbar-color: $primary transparent; /* Replace with your primary color variable */
}

input:-webkit-autofill,
input:-webkit-autofill:focus {
  background-color: transparent !important;
}

// src/assets/scss/main.scss

// Import Quasar variables first
@import '~quasar/src/css/variables.sass';

/**
// Global Styles
body {
  font-family: $font-family;
  background-color: #f9fafb; // Gray 50
  color: #111827; // Gray 900
}

// Typography System
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: $header-font;
  line-height: 1.2;
  margin-bottom: $spacing-md;
}

h1 {
  font-size: 2.5rem;
}
h2 {
  font-size: 2rem;
}
h3 {
  font-size: 1.75rem;
}
h4 {
  font-size: 1.5rem;
}
h5 {
  font-size: 1.25rem;
}
h6 {
  font-size: 1rem;
}

p {
  margin-bottom: $spacing-md;
  line-height: 1.6;
}

a {
  color: $primary-color;
  text-decoration: none;
  transition: color 0.2s ease;

  &:hover {
    color: darken($primary-color, 10%);
  }
}

// Card Components
.q-card {
  border-radius: $border-radius-lg;
  box-shadow: $card-shadow;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
  &.manny-book-card {
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 24px rgba(0, 0, 0, 0.1);
    }
  }

  .q-card-section {
    padding: $spacing-lg;
  }
}

// Buttons
.q-btn {
  font-weight: 600;
  transition: all 0.2s ease;
  &.q-btn--primary {
    background: linear-gradient(
      135deg,
      $primary-color 0%,
      darken($primary-color, 10%) 100%
    );
    &:hover {
      transform: translateY(-1px);
    }
  }

  &.q-btn--secondary {
    background: linear-gradient(
      135deg,
      $secondary-color 0%,
      darken($secondary-color, 10%) 100%
    );
  }
}

// Transitions
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}
.slide-enter-from,
.slide-leave-to {
  transform: translateY(20px);
  opacity: 0;
}

// Utility Classes

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-primary {
  color: $primary-color;
}

.text-secondary {
  color: $secondary-color;
}

.bg-primary-gradient {
  background: linear-gradient(
    135deg,
    $primary-color 0%,
    darken($primary-color, 10%) 100%
  );
}

.rounded-md {
  border-radius: $border-radius-md;
}

.rounded-lg {
  border-radius: $border-radius-lg;
}

.shadow-sm {
  box-shadow: $card-shadow;
}

.gap-xs {
  gap: $spacing-xs;
}

.gap-sm {
  gap: $spacing-sm;
}

.gap-md {
  gap: $spacing-md;
}

.gap-lg {
  gap: $spacing-lg;
}

.p-xs {
  padding: $spacing-xs;
}

.p-sm {
  padding: $spacing-sm;
}

.p-md {
  padding: $spacing-md;
}

.p-lg {
  padding: $spacing-lg;
}

.m-xs {
  margin: $spacing-xs;
}

.m-sm {
  margin: $spacing-sm;
}

.m-md {
  margin: $spacing-md;
}

.m-lg {
  margin: $spacing-lg;
}
**/
