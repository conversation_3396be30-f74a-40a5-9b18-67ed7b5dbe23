// Quasar SCSS (& Sass) Variables
// --------------------------------------------------
// To customize the look and feel of this app, you can override
// the Sass/SCSS variables found in Quasar's source Sass/SCSS files.

// Check documentation for full list of Quasar variables

// Your own variables (that are declared here) and Quasar's own
// ones will be available out of the box in your .vue/.scss/.sass files

// It's highly recommended to change the default colors
// to match your app's branding.
// Tip: Use the "Theme Builder" on Quasar's documentation website.

$primary: #00569b;
.body--dark {
  --q-dark: #121212;
  background: #121212 !important;
}
$secondary: #00569b;
$accent: #00569b;

$dark: #717171;

$positive: #21ba45;
$negative: #c10015;
$info: #00569b;
$yellow: #ffd940;
$gray-500: #adb5bd;

// Theme Colors
$primary-color: #3b82f6; // Tailwind Blue 500
$secondary-color: #10b981; // Emerald 500
$accent-color: #8b5cf6; // Violet 500
$neutral-color: #6b7280; // Gray 500
$error-color: #ef4444; // Red 500
$success-color: #10b981; // Green 500

// Typography
$font-family: 'Inter', 'Roboto', sans-serif;
$header-font: 'Poppins', $font-family;

// Spacing System
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// Shadows
$card-shadow: 0 4px 18px rgba(0, 0, 0, 0.08);
$input-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
$button-hover-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

// Border Radius
$border-radius-sm: 8px;
$border-radius-md: 12px;
$border-radius-lg: 16px;
