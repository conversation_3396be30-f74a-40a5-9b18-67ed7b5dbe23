<template>
  <section
    id="export"
    class="q-mx-auto container"
    :class="{
      'q-pa-lg': !$q.screen.lt.sm,
    }"
  >
    <q-card
      class="category-card shadow-3"
      :class="{
        'full-width': $q.screen.lt.sm,
      }"
    >
      <!-- Header Section -->
      <q-card-section class="bg-primary text-white q-pb-md">
        <div class="row items-center">
          <q-icon name="category" size="2em" class="q-mr-md" />
          <div class="text-h5">Book Category Manager</div>
        </div>
      </q-card-section>

      <!-- Content Section -->
      <q-card-section class="q-pa-none">
        <div class="row">
          <!-- Category List Column -->
          <div class="col-12 col-md-4 category-sidebar">
            <div class="q-pa-md">
              <div class="row justify-between items-center q-mb-md">
                <div class="text-h6">Categories</div>
                <q-btn
                  color="primary"
                  icon="add"
                  round
                  dense
                  @click="createCategory"
                  :loading="savingCategory"
                >
                  <q-tooltip>Add Category</q-tooltip>
                </q-btn>
              </div>

              <q-list bordered separator class="category-list rounded-borders">
                <template v-if="categories.length > 0">
                  <q-item
                    v-for="category in categories"
                    :key="category.id"
                    clickable
                    :active="activeCategory?.id === category.id"
                    @click="setActiveCategory(category.id)"
                    v-ripple
                  >
                    <q-item-section avatar>
                      <q-avatar color="primary" text-color="white">
                        <q-icon :name="category.icon || 'category'" />
                      </q-avatar>
                    </q-item-section>
                    <q-item-section>
                      <q-item-label class="text-weight-medium">{{
                        category.title
                      }}</q-item-label>
                      <q-item-label caption>
                        <q-icon name="help_outline" size="xs" class="q-mr-xs" />
                        {{ category.questions?.length || 0 }} questions
                      </q-item-label>
                    </q-item-section>
                    <q-item-section side>
                      <div class="row items-center">
                        <q-btn
                          flat
                          round
                          dense
                          icon="edit"
                          color="primary"
                          @click.stop="editCategory(category)"
                          :loading="savingCategory"
                        >
                          <q-tooltip>Edit Category</q-tooltip>
                        </q-btn>
                        <q-btn
                          flat
                          round
                          dense
                          icon="delete"
                          color="negative"
                          @click.stop="deleteCategory(category)"
                          :loading="deletingCategory"
                          class="q-ml-xs"
                        >
                          <q-tooltip>Delete Category</q-tooltip>
                        </q-btn>
                      </div>
                    </q-item-section>
                  </q-item>
                </template>
                <template v-else>
                  <q-item class="text-center">
                    <q-item-section>
                      <div class="empty-list-message q-py-md">
                        <q-icon
                          name="category_off"
                          size="48px"
                          color="grey-5"
                        />
                        <div class="text-subtitle1 text-grey-8 q-mt-sm">
                          No categories yet
                        </div>
                        <div class="text-caption text-grey-7 q-mb-sm">
                          Create your first category to get started
                        </div>
                      </div>
                    </q-item-section>
                  </q-item>
                </template>

                <q-inner-loading
                  :showing="
                    loadingCategories || loading || loadingSubcategories
                  "
                  color="primary"
                  label-class="text-primary"
                  label-style="font-size: 1.1em"
                  style="z-index: 1"
                >
                  <q-spinner-dots color="primary" size="2em" />
                  <div class="align-center">
                    <q-icon name="img:robot.png" class="q-ml-xs" size="xl" />
                    {{
                      loadingCategories
                        ? 'Loading Categories...'
                        : loadingSubcategories
                        ? 'Loading Subcategories...'
                        : savingCategory || savingSubcategory || savingQuestion
                        ? 'Saving...'
                        : deletingCategory || deletingSubcategory
                        ? 'Deleting...'
                        : loadingQuestions
                        ? 'Loading Questions...'
                        : 'Loading...'
                    }}
                  </div>
                </q-inner-loading>
              </q-list>
            </div>
          </div>

          <!-- Category Details Column -->
          <div class="col-12 col-md-8 category-details">
            <div v-if="activeCategory" class="q-pa-md">
              <div class="row items-center q-mb-md">
                <q-avatar
                  color="primary"
                  text-color="white"
                  size="42px"
                  class="q-mr-md"
                >
                  <q-icon
                    :name="activeCategory.icon || 'category'"
                    size="28px"
                  />
                </q-avatar>
                <div>
                  <div class="text-h5 text-weight-medium">
                    {{ activeCategory.title }}
                  </div>
                  <div class="text-caption text-grey-7">
                    <q-icon name="help_outline" size="xs" class="q-mr-xs" />
                    {{ questions.length }} questions in this category
                  </div>
                </div>
              </div>
              <q-separator class="q-mb-md" />

              <!-- Subcategories Section -->
              <div class="row justify-between items-center q-mb-md">
                <div class="text-subtitle1 text-weight-medium">
                  <q-icon name="folder" class="q-mr-xs" />
                  Subcategories
                </div>
                <q-btn
                  color="amber"
                  label="Add Subcategory"
                  icon="add"
                  unelevated
                  @click="showSubcategoryInForm"
                  :loading="savingSubcategory"
                />
              </div>

              <div v-if="subcategories.length > 0">
                <q-list
                  bordered
                  separator
                  class="rounded-borders subcategory-list"
                >
                  <q-item
                    v-for="subcategory in subcategories"
                    :key="subcategory.id"
                    class="subcategory-item"
                    clickable
                    :active="selectedSubcategory?.id === subcategory.id"
                    @click="selectSubcategory(subcategory)"
                    v-ripple
                  >
                    <q-item-section avatar>
                      <q-avatar color="amber" text-color="white">
                        <q-icon :name="subcategory.icon || 'folder'" />
                      </q-avatar>
                    </q-item-section>
                    <q-item-section>
                      <q-item-label class="text-weight-medium">
                        {{ subcategory.name }}
                      </q-item-label>
                      <q-item-label caption>
                        <q-icon name="help_outline" size="xs" class="q-mr-xs" />
                        {{ getQuestionsCountBySubcategory(subcategory.id) }}
                        questions
                      </q-item-label>
                    </q-item-section>
                    <q-item-section
                      side
                      v-if="subcategory.name !== 'Uncategorized'"
                    >
                      <div class="row items-center">
                        <q-btn
                          flat
                          round
                          dense
                          icon="edit"
                          color="amber"
                          @click.stop="editSubcategory(subcategory)"
                          :loading="savingSubcategory"
                        >
                          <q-tooltip>Edit Subcategory</q-tooltip>
                        </q-btn>
                        <q-btn
                          flat
                          round
                          dense
                          icon="delete"
                          color="negative"
                          @click.stop="deleteSubcategory(subcategory.id)"
                          :loading="deletingSubcategory"
                          class="q-ml-xs"
                        >
                          <q-tooltip>Delete Subcategory</q-tooltip>
                        </q-btn>
                      </div>
                    </q-item-section>
                  </q-item>
                </q-list>
              </div>
              <div v-else class="text-center q-pa-lg empty-state">
                <q-icon name="folder_off" size="48px" color="grey-5" />
                <div class="text-h6 text-grey-8 q-mt-md">
                  No subcategories yet
                </div>
                <div class="text-caption text-grey-7 q-mb-md">
                  Create subcategories to organize your questions
                </div>
                <q-btn
                  color="amber"
                  label="Add Subcategory"
                  icon="add"
                  unelevated
                  @click="showSubcategoryInForm"
                  :loading="savingSubcategory"
                />
              </div>

              <!-- Questions Section -->
              <div>
                <div class="row justify-between items-center q-mb-md q-mt-lg">
                  <div class="text-subtitle1 text-weight-medium">
                    <q-icon name="quiz" class="q-mr-xs" />
                    Questions
                    <span
                      v-if="selectedSubcategory"
                      class="text-caption q-ml-sm"
                    >
                      in
                      <q-chip
                        size="sm"
                        class="chip-padding"
                        color="amber"
                        text-color="white"
                        dense
                      >
                        <q-icon
                          :name="selectedSubcategory.icon"
                          size="xs"
                          class="q-mr-xs"
                        />
                        {{ selectedSubcategory.name }}
                      </q-chip>
                    </span>
                  </div>
                  <q-btn
                    color="primary"
                    label="Add Question"
                    icon="add"
                    unelevated
                    @click="showQuestionForm"
                    :disable="!selectedSubcategory"
                    :loading="savingQuestion"
                  >
                    <q-tooltip v-if="!selectedSubcategory">
                      Select a subcategory first
                    </q-tooltip>
                  </q-btn>
                </div>

                <div
                  v-if="!selectedSubcategory"
                  class="text-center q-pa-lg empty-state"
                >
                  <q-icon
                    name="subdirectory_arrow_right"
                    size="48px"
                    color="amber"
                  />
                  <div class="text-h6 text-grey-8 q-mt-md">
                    Select a subcategory
                  </div>
                  <div class="text-caption text-grey-7 q-mb-md">
                    Choose a subcategory to view or add questions
                  </div>
                </div>

                <div v-else-if="filteredQuestions.length > 0">
                  <q-list
                    bordered
                    separator
                    class="rounded-borders question-list"
                  >
                    <q-item
                      v-for="question in filteredQuestions"
                      :key="question.id"
                      class="question-item q-py-md"
                    >
                      <q-item-section avatar>
                        <q-avatar
                          :color="answerElementColor[question.answerElement]"
                          text-color="white"
                        >
                          <q-icon name="help" />
                        </q-avatar>
                      </q-item-section>
                      <q-item-section>
                        <q-item-label class="text-weight-medium">{{
                          question.question
                        }}</q-item-label>
                        <q-item-label
                          caption
                          v-if="question.description"
                          class="q-mt-xs"
                        >
                          {{ question.description }}
                        </q-item-label>
                        <div class="row q-mt-sm">
                          <q-chip
                            size="sm"
                            :color="answerElementColor[question.answerElement]"
                            text-color="white"
                            dense
                            class="q-mr-sm chip-padding"
                          >
                            <q-icon
                              left
                              :name="
                                getAnswerElementIcon(question.answerElement)
                              "
                              size="xs"
                            />
                            {{ question.answerElement }}
                          </q-chip>
                          <q-chip
                            v-if="question.hasAI"
                            size="sm"
                            color="primary"
                            text-color="white"
                            dense
                            class="chip-padding"
                          >
                            <q-icon left name="img:robot.png" size="xs" />
                            Manny
                          </q-chip>
                        </div>
                      </q-item-section>
                      <q-item-section side>
                        <div class="row items-center">
                          <q-btn
                            flat
                            round
                            dense
                            icon="edit"
                            color="primary"
                            @click="editQuestion(question)"
                          >
                            <q-tooltip>Edit Question</q-tooltip>
                          </q-btn>
                          <q-btn
                            flat
                            round
                            dense
                            icon="delete"
                            color="negative"
                            @click="deleteQuestion(question)"
                            class="q-ml-xs"
                          >
                            <q-tooltip>Delete Question</q-tooltip>
                          </q-btn>
                        </div>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </div>
                <div v-else class="text-center q-pa-lg empty-state">
                  <q-icon name="help_outline" size="64px" color="grey-5" />
                  <div class="text-h6 text-grey-8 q-mt-md">
                    No questions in this subcategory
                  </div>
                  <div class="text-caption text-grey-7 q-mb-md">
                    Add questions to help guide book creation
                  </div>
                  <q-btn
                    color="primary"
                    label="Add Question"
                    icon="add"
                    unelevated
                    @click="showQuestionForm"
                  />
                </div>
              </div>
            </div>
            <div v-else class="no-category q-pa-xl">
              <q-card flat class="no-category-card text-center">
                <q-card-section>
                  <q-icon name="category" size="64px" color="grey-5" />
                  <div class="text-h6 text-grey-8 q-mt-md">
                    {{
                      categories.length > 0
                        ? 'Select a category'
                        : 'No categories yet'
                    }}
                  </div>
                  <div class="text-caption text-grey-7 q-mb-md">
                    {{
                      categories.length > 0
                        ? 'to view or manage questions'
                        : 'Create your first category to get started'
                    }}
                  </div>
                  <q-btn
                    v-if="categories.length === 0"
                    color="primary"
                    label="Add Category"
                    icon="add"
                    unelevated
                    @click="createCategory"
                    class="q-mt-sm"
                  />
                </q-card-section>
              </q-card>
            </div>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Category Form Dialog -->
    <q-dialog v-model="showCategoryForm" persistent>
      <q-card style="min-width: 400px" class="category-form-dialog">
        <q-card-section class="bg-primary text-white">
          <div class="text-h6">
            {{ editingCategory ? 'Edit' : 'Add' }} Category
          </div>
        </q-card-section>
        <q-card-section class="q-pt-lg">
          <q-form @submit="saveCategory" class="q-gutter-md">
            <q-input
              v-model="categoryForm.title"
              label="Title"
              :rules="[(val) => !!val || 'Title is required']"
              outlined
              autofocus
            />
            <q-select
              v-model="categoryForm.icon"
              :options="iconOptions"
              label="Icon"
              :rules="[(val) => !!val || 'Icon is required']"
              outlined
              emit-value
              map-options
            >
              <template v-slot:option="scope">
                <q-item v-bind="scope.itemProps">
                  <q-item-section avatar>
                    <q-icon :name="scope.opt.value" />
                  </q-item-section>
                  <q-item-section>{{ scope.opt.label }}</q-item-section>
                </q-item>
              </template>
              <template v-slot:selected>
                <div class="row items-center">
                  <q-icon :name="categoryForm.icon" class="q-mr-sm" />
                  {{ categoryForm.icon }}
                </div>
              </template>
            </q-select>
          </q-form>
        </q-card-section>
        <q-card-actions align="right" class="q-pa-md">
          <q-btn flat label="Cancel" color="grey-7" v-close-popup />
          <q-btn
            unelevated
            label="Save"
            color="primary"
            @click="saveCategory"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- Subcategory Form Dialog -->
    <q-dialog v-model="showSubcategoryForm" persistent>
      <q-card style="min-width: 400px" class="subcategory-form-dialog">
        <q-card-section class="bg-amber text-white">
          <div class="text-h6">
            {{ editingSubcategory ? 'Edit' : 'Add' }} Subcategory
          </div>
        </q-card-section>
        <q-card-section class="q-pt-lg">
          <q-form @submit="saveSubcategory" class="q-gutter-md">
            <q-input
              v-model="subcategoryForm.name"
              label="Name"
              :rules="[(val) => !!val || 'Name is required']"
              outlined
              autofocus
            >
              <template v-slot:prepend>
                <q-icon name="folder" color="amber" />
              </template>
            </q-input>
            <q-select
              v-model="subcategoryForm.icon"
              :options="folderIconOptions"
              label="Icon"
              :rules="[(val) => !!val || 'Icon is required']"
              outlined
              emit-value
              map-options
            >
              <template v-slot:option="scope">
                <q-item v-bind="scope.itemProps">
                  <q-item-section avatar>
                    <q-icon :name="scope.opt.value" color="amber" />
                  </q-item-section>
                  <q-item-section>{{ scope.opt.label }}</q-item-section>
                </q-item>
              </template>
              <template v-slot:selected>
                <div class="row items-center">
                  <q-icon
                    :name="subcategoryForm.icon"
                    color="amber"
                    class="q-mr-sm"
                  />
                  {{ subcategoryForm.icon }}
                </div>
              </template>
            </q-select>
          </q-form>
        </q-card-section>
        <q-card-actions align="right" class="q-pa-md">
          <q-btn flat label="Cancel" color="grey-7" v-close-popup />
          <q-btn
            unelevated
            label="Save"
            color="amber"
            text-color="white"
            @click="saveSubcategory"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- Question Form Dialog -->
    <q-dialog v-model="showQuestionDialog" persistent>
      <q-card
        style="
          min-width: 700px;
          max-height: 90vh;
          display: flex;
          flex-direction: column;
        "
      >
        <q-card-section
          class="bg-primary text-white q-py-sm"
          style="position: sticky; top: 0; z-index: 10"
        >
          <div class="text-h6">
            {{ editingQuestion ? 'Edit Question' : 'Create New Question' }}
            <span v-if="selectedSubcategory" class="text-caption q-ml-sm">
              in {{ selectedSubcategory.name }}
            </span>
          </div>
        </q-card-section>

        <q-card-section
          class="scroll"
          style="flex: 1; overflow-y: auto; padding-bottom: 70px"
        >
          <QuestionForm
            v-model="questionForm"
            :available-variables="categoryQuestionVariables"
            :subcategory-options="subcategoryOptions"
            @submit="saveQuestion"
          />
        </q-card-section>

        <q-card-actions
          align="right"
          class="bg-white q-py-md"
          style="position: sticky; bottom: 0; z-index: 10"
        >
          <q-btn
            flat
            label="Cancel"
            color="grey-7"
            @click="closeQuestionDialog"
          />
          <q-btn
            unelevated
            label="Save"
            color="primary"
            @click="saveQuestion"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </section>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { storeToRefs } from 'pinia';
import { useCategoryStore } from 'src/entities/book-category/store/useCategoryStore';
import { useQuestionStore } from 'src/entities/question/store/useQuestionStore';
import {
  Question,
  AIOptions,
  AnswerElement,
} from 'src/entities/question/model';
import { QuestionForm } from 'src/entities/question';
import { BookCategory, Subcategory } from 'src/entities/book-category';

const $q = useQuasar();
const categoryStore = useCategoryStore();
const questionStore = useQuestionStore();
const { categories, activeCategory, loading } = storeToRefs(categoryStore);
const { questions, subcategories } = storeToRefs(questionStore);

// Form state for categories
const showCategoryForm = ref(false);
const editingCategory = ref<string | null>(null);
const categoryForm = ref({
  title: '',
  icon: 'category',
});

// Form state for subcategories
const showSubcategoryForm = ref(false);
const editingSubcategory = ref<Subcategory | null>(null);
const subcategoryForm = ref({
  name: '',
  icon: 'folder',
});

// Form state for questions
const showQuestionDialog = ref(false);
const editingQuestion = ref<string | null>(null);
const questionForm = ref<Question>({} as Question);

// Default AI options
const defaultAIOptions = ref<AIOptions>({
  temperature: 0.7,
  maxTokens: 500,
  topP: 1,
  frequencyPenalty: 0,
  presencePenalty: 0,
});

// Selected subcategory state
const selectedSubcategory = ref<Subcategory | null>(null);

// Add loading state for different actions
const loadingCategories = ref(false);
const loadingSubcategories = ref(false);
const loadingQuestions = ref(false);
const savingCategory = ref(false);
const savingSubcategory = ref(false);
const savingQuestion = ref(false);
const deletingCategory = ref(false);
const deletingSubcategory = ref(false);
const deletingQuestion = ref(false);

// Create default "Uncategorized" subcategory for a category
async function createUncategorizedSubcategory(
  categoryId: string,
): Promise<string | null> {
  try {
    // Check if Uncategorized subcategory already exists
    const existingSubcategories = await questionStore.fetchSubcategories(
      categoryId,
    );
    const hasUncategorized = existingSubcategories.some(
      (sub) => sub.name.toLowerCase() === 'uncategorized',
    );

    if (!hasUncategorized) {
      const subcategoryId = await questionStore.createSubcategory({
        categoryId: categoryId,
        name: 'Uncategorized',
        icon: 'folder_off',
        isDefault: true,
      });

      console.log('Created Uncategorized subcategory:', subcategoryId);
      return subcategoryId;
    }

    // Return the existing uncategorized subcategory id
    return (
      existingSubcategories.find(
        (sub) => sub.name.toLowerCase() === 'uncategorized',
      )?.id || null
    );
  } catch (error) {
    console.error('Error creating uncategorized subcategory:', error);
    $q.notify({
      color: 'negative',
      message: 'Failed to create default subcategory',
      icon: 'error',
      position: 'top',
    });
    return null;
  }
}

// Select a subcategory
function selectSubcategory(subcategory: Subcategory) {
  selectedSubcategory.value = subcategory;
}

// Computed property for filtered questions based on selected subcategory
const filteredQuestions = computed(() => {
  if (!selectedSubcategory.value) return [];
  loadingQuestions.value = true;
  const filtered = questions.value.filter(
    (q) => q.subcategoryId === selectedSubcategory.value?.id,
  );
  loadingQuestions.value = false;
  return filtered;
});

// Get questions count by subcategory
function getQuestionsCountBySubcategory(subcategoryId: string): number {
  return questions.value.filter((q) => q.subcategoryId === subcategoryId)
    .length;
}

// Icon options for categories
const iconOptions = [
  { label: 'Category', value: 'category' },
  { label: 'Book', value: 'book' },
  { label: 'Library', value: 'local_library' },
  { label: 'School', value: 'school' },
  { label: 'Science', value: 'science' },
  { label: 'History', value: 'history' },
  { label: 'Business', value: 'business' },
  { label: 'Psychology', value: 'psychology' },
  { label: 'Health', value: 'health_and_safety' },
  { label: 'Travel', value: 'travel_explore' },
  { label: 'Food', value: 'restaurant' },
  { label: 'Sports', value: 'sports' },
  { label: 'Art', value: 'palette' },
  { label: 'Music', value: 'music_note' },
  { label: 'Technology', value: 'devices' },
  { label: 'Nature', value: 'nature' },
];

// Icon options for folders/subcategories
const folderIconOptions = [
  { label: 'folder', value: 'folder' },
  { label: 'folder_open', value: 'folder_open' },
  { label: 'folder_shared', value: 'folder_shared' },
  { label: 'folder_special', value: 'folder_special' },
  { label: 'folder_zip', value: 'folder_zip' },
  { label: 'folder_off', value: 'folder_off' },
  ...iconOptions,
];

onMounted(async () => {
  loadingCategories.value = true;
  try {
    await categoryStore.fetchCategories();

    // Auto-select first category if available
    if (categories.value.length > 0 && !activeCategory.value) {
      await setActiveCategory(categories.value[0].id);
    }
  } catch (error) {
    console.error('Error loading categories:', error);
    $q.notify({
      color: 'negative',
      message: 'Failed to load categories',
      icon: 'error',
      position: 'top',
    });
  } finally {
    loadingCategories.value = false;
  }
});

async function setActiveCategory(categoryId: string) {
  loadingSubcategories.value = true;
  try {
    await categoryStore.setActiveCategory(categoryId);
    await questionStore.fetchAllBySubcategory(categoryId);

    // Auto-select first subcategory if available
    if (subcategories.value.length > 0) {
      // Try to find a subcategory with questions first
      const subcategoryWithQuestions = subcategories.value.find((sub) =>
        questions.value.some((q) => q.subcategoryId === sub.id),
      );

      if (subcategoryWithQuestions) {
        selectedSubcategory.value = subcategoryWithQuestions;
      } else {
        // Otherwise select the first subcategory
        selectedSubcategory.value = subcategories.value[0];
      }
    } else {
      selectedSubcategory.value = null;
    }
  } catch (error) {
    console.error('Error setting active category:', error);
    $q.notify({
      color: 'negative',
      message: 'Failed to load category details',
      icon: 'error',
      position: 'top',
    });
  } finally {
    loadingSubcategories.value = false;
  }
}

// Category management functions
function editCategory(category: BookCategory) {
  editingCategory.value = category.id;
  categoryForm.value = {
    title: category.title,
    icon: category.icon || 'category',
  };
  showCategoryForm.value = true;
}

function createCategory() {
  editingCategory.value = null;
  categoryForm.value = {
    title: '',
    icon: 'category',
  };
  showCategoryForm.value = true;
}

async function saveCategory() {
  if (!categoryForm.value.title || !categoryForm.value.icon) {
    $q.notify({
      color: 'negative',
      message: 'Please fill all required fields',
      icon: 'warning',
      position: 'top',
    });
    return;
  }

  savingCategory.value = true;
  try {
    if (editingCategory.value) {
      // Update existing category
      const success = await categoryStore.updateCategory(
        editingCategory.value,
        {
          title: categoryForm.value.title,
          icon: categoryForm.value.icon,
        },
      );

      if (success) {
        $q.notify({
          color: 'positive',
          message: 'Category updated successfully',
          icon: 'check',
          position: 'top',
        });
      }
    } else {
      // Create new category
      const id = await categoryStore.createCategory({
        title: categoryForm.value.title,
        icon: categoryForm.value.icon,
      });

      if (id) {
        // Create default "Uncategorized" subcategory for the new category
        await createUncategorizedSubcategory(id);

        $q.notify({
          color: 'positive',
          message: 'Category created successfully',
          icon: 'check',
          position: 'top',
        });

        // Set this as the active category
        await setActiveCategory(id);
      }
    }
    showCategoryForm.value = false;
  } catch (error) {
    $q.notify({
      color: 'negative',
      message: 'An error occurred',
      icon: 'error',
      position: 'top',
    });
  } finally {
    savingCategory.value = false;
  }
}

function deleteCategory(category: BookCategory) {
  $q.dialog({
    title: 'Confirm Deletion',
    message: `Are you sure you want to delete "${category.title}"?`,
    cancel: true,
    persistent: true,
    ok: {
      color: 'negative',
      label: 'Delete',
      unelevated: true,
    },
    cancel: {
      flat: true,
      label: 'Cancel',
    },
  }).onOk(async () => {
    deletingCategory.value = true;
    try {
      const success = await categoryStore.deleteCategory(category.id);
      if (success) {
        $q.notify({
          color: 'positive',
          message: 'Category deleted successfully',
          icon: 'check',
          position: 'top',
        });
      }
    } catch (error) {
      $q.notify({
        color: 'negative',
        message: 'Failed to delete category',
        icon: 'error',
        position: 'top',
      });
    } finally {
      deletingCategory.value = false;
    }
  });
}

// Subcategory management functions
function showSubcategoryInForm() {
  if (!activeCategory.value) {
    $q.notify({
      color: 'negative',
      message: 'Please select a category first',
      icon: 'warning',
      position: 'top',
    });
    return;
  }

  editingSubcategory.value = null;
  subcategoryForm.value = {
    name: '',
    icon: 'folder',
  };
  showSubcategoryForm.value = true;
}

function editSubcategory(subcategory: { id: string; name: string }) {
  // Don't allow editing the default Uncategorized subcategory
  if (subcategory.name.toLowerCase() === 'uncategorized') {
    $q.notify({
      color: 'warning',
      message: 'The Uncategorized subcategory cannot be edited',
      icon: 'warning',
      position: 'top',
    });
    return;
  }

  editingSubcategory.value = { id: subcategory.id, name: subcategory.name };
  subcategoryForm.value = {
    name: subcategory.name,
    icon: subcategory.icon || 'folder',
  };
  showSubcategoryForm.value = true;
}

async function saveSubcategory() {
  if (!subcategoryForm.value.name) {
    $q.notify({
      color: 'negative',
      message: 'Subcategory name is required',
      icon: 'warning',
      position: 'top',
    });
    return;
  }

  // Don't allow creating a subcategory named "Uncategorized"
  if (
    subcategoryForm.value.name.toLowerCase() === 'uncategorized' &&
    !editingSubcategory.value
  ) {
    $q.notify({
      color: 'warning',
      message:
        'The name "Uncategorized" is reserved for the default subcategory',
      icon: 'warning',
      position: 'top',
    });
    return;
  }

  savingSubcategory.value = true;
  try {
    if (editingSubcategory.value) {
      // Update existing subcategory
      const success = await questionStore.updateSubcategory(
        activeCategory.value!.id,
        editingSubcategory.value.id,
        {
          name: subcategoryForm.value.name,
          icon: subcategoryForm.value.icon,
        },
      );

      if (success) {
        $q.notify({
          color: 'positive',
          message: 'Subcategory updated successfully',
          icon: 'check',
          position: 'top',
        });
      }
    } else {
      // Create new subcategory
      const subcategoryId = await questionStore.createSubcategory({
        categoryId: activeCategory.value!.id,
        name: subcategoryForm.value.name,
        icon: subcategoryForm.value.icon,
      });

      if (subcategoryId) {
        $q.notify({
          color: 'positive',
          message: 'Subcategory created successfully',
          icon: 'check',
          position: 'top',
        });

        // Select the newly created subcategory
        const newSubcategory = subcategories.value.find(
          (s) => s.id === subcategoryId,
        );
        if (newSubcategory) {
          selectedSubcategory.value = newSubcategory;
        }
      }
    }
    showSubcategoryForm.value = false;

    // Refresh questions to see the changes
    if (activeCategory.value) {
      await questionStore.fetchAllBySubcategory(activeCategory.value.id);
    }
  } catch (error) {
    $q.notify({
      color: 'negative',
      message: 'An error occurred',
      icon: 'error',
      position: 'top',
    });
  } finally {
    savingSubcategory.value = false;
  }
}

function deleteSubcategory(subcategoryId: string) {
  // Find the subcategory
  const subcategory = subcategories.value.find((s) => s.id === subcategoryId);

  // Don't allow deleting the default Uncategorized subcategory
  if (subcategory && subcategory.name.toLowerCase() === 'uncategorized') {
    $q.notify({
      color: 'warning',
      message: 'The Uncategorized subcategory cannot be deleted',
      icon: 'warning',
      position: 'top',
    });
    return;
  }

  $q.dialog({
    title: 'Confirm Deletion',
    message:
      'Are you sure you want to delete this subcategory? Questions will be moved to Uncategorized.',
    cancel: true,
    persistent: true,
    ok: {
      color: 'negative',
      label: 'Delete',
      unelevated: true,
    },
    cancel: {
      flat: true,
      label: 'Cancel',
    },
  }).onOk(async () => {
    deletingSubcategory.value = true;
    try {
      // Find the Uncategorized subcategory
      const uncategorizedSub = subcategories.value.find(
        (s) => s.name.toLowerCase() === 'uncategorized',
      );

      // If we don't have an Uncategorized subcategory, create one
      let uncategorizedId = uncategorizedSub?.id;
      if (!uncategorizedId && activeCategory.value) {
        uncategorizedId = await createUncategorizedSubcategory(
          activeCategory.value.id,
        );
      }

      // Move questions to Uncategorized before deleting
      const questionsToMove = questions.value.filter(
        (q) => q.subcategoryId === subcategoryId,
      );
      for (const question of questionsToMove) {
        await questionStore.update(question.id, {
          ...question,
          subcategoryId: uncategorizedId,
          subcategory: 'Uncategorized',
        });
      }

      // Now delete the subcategory
      const success = await questionStore.deleteSubcategory(subcategoryId);
      if (success) {
        $q.notify({
          color: 'positive',
          message: 'Subcategory deleted successfully',
          icon: 'check',
          position: 'top',
        });
      }

      // Refresh questions to see the changes
      if (activeCategory.value) {
        await questionStore.fetchAllBySubcategory(activeCategory.value.id);
      }
    } catch (error) {
      $q.notify({
        color: 'negative',
        message: 'An error occurred',
        icon: 'error',
        position: 'top',
      });
    } finally {
      deletingSubcategory.value = false;
    }
  });
}

// Question management functions
function showQuestionForm() {
  if (!activeCategory.value) {
    $q.notify({
      color: 'negative',
      message: 'Please select a category first',
      icon: 'warning',
      position: 'top',
    });
    return;
  }

  // Find the Uncategorized subcategory
  const uncategorizedSub = subcategories.value.find(
    (s) => s.name.toLowerCase() === 'uncategorized',
  );

  editingQuestion.value = null;
  questionForm.value = {
    id: '',
    categoryId: activeCategory.value.id,
    subcategoryId: selectedSubcategory.value.id as string, // Use the selected subcategory's ID
    subcategoryObject: selectedSubcategory.value as Subcategory,
    subcategory: selectedSubcategory.value.name as string,
    question: '',
    description: '',
    hasAI: false,
    aiPrompt: '',
    aiOptions: { ...defaultAIOptions.value },
    answerElement: 'text',
    options: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  showQuestionDialog.value = true;
}

function editQuestion(question: Question) {
  editingQuestion.value = question.id;
  questionForm.value = question;

  // Ensure aiOptions is properly initialized
  if (!questionForm.value.aiOptions) {
    questionForm.value.aiOptions = { ...defaultAIOptions.value };
  }

  // Ensure options array is initialized
  if (!questionForm.value.options) {
    questionForm.value.options = [];
  }

  showQuestionDialog.value = true;
}

function deleteQuestion(question: Question) {
  $q.dialog({
    title: 'Confirm Deletion',
    message: `Are you sure you want to delete this question?`,
    cancel: true,
    persistent: true,
    ok: {
      color: 'negative',
      label: 'Delete',
      unelevated: true,
    },
    cancel: {
      flat: true,
      label: 'Cancel',
    },
  }).onOk(async () => {
    deletingQuestion.value = true;
    try {
      const success = await questionStore.delete(question.id);
      if (success) {
        $q.notify({
          color: 'positive',
          message: 'Question deleted successfully',
          icon: 'check',
          position: 'top',
        });
      }
    } catch (error) {
      $q.notify({
        color: 'negative',
        message: 'Failed to delete question',
        icon: 'error',
        position: 'top',
      });
    } finally {
      deletingQuestion.value = false;
    }
  });
}

async function saveQuestion() {
  savingQuestion.value = true;
  try {
    if (editingQuestion.value) {
      // Update existing question
      const success = await questionStore.update(
        editingQuestion.value,
        questionForm.value,
      );

      if (success) {
        $q.notify({
          color: 'positive',
          message: 'Question updated successfully',
          icon: 'check',
          position: 'top',
        });
      }
    } else {
      // Create new question
      const id = await questionStore.create(questionForm.value);

      if (id) {
        $q.notify({
          color: 'positive',
          message: 'Question created successfully',
          icon: 'check',
          position: 'top',
        });
      }
    }
    closeQuestionDialog();
  } catch (error) {
    $q.notify({
      color: 'negative',
      message: 'An error occurred',
      icon: 'error',
      position: 'top',
    });
  } finally {
    savingQuestion.value = false;
  }
}

// Colors for answer elements
const answerElementColor = ref<Record<AnswerElement, string>>({
  text: 'blue',
  textarea: 'indigo',
  select: 'green',
  options: 'orange',
});

function closeQuestionDialog() {
  showQuestionDialog.value = false;
}

// Helper function to get icon for answer element
function getAnswerElementIcon(answerElement: string): string {
  const icons = {
    text: 'short_text',
    textarea: 'notes',
    select: 'arrow_drop_down_circle',
    options: 'radio_button_checked',
  };
  return icons[answerElement] || 'help';
}

// Computed property to group questions by subcategory
const groupedQuestions = computed(() => {
  const groups: { name: string; questions: Question[] }[] = [];
  const subcategories = new Set<string>();

  // First, collect all subcategories
  questions.value.forEach((question) => {
    if (question.subcategory) {
      subcategories.add(question.subcategory);
    }
  });

  // Create groups for each subcategory
  subcategories.forEach((subcategory) => {
    groups.push({
      name: subcategory,
      questions: questions.value.filter((q) => q.subcategory === subcategory),
    });
  });

  // Add uncategorized questions
  const uncategorized = questions.value.filter((q) => !q.subcategory);
  if (uncategorized.length > 0) {
    groups.push({
      name: '',
      questions: uncategorized,
    });
  }

  return groups;
});

// Get unique subcategories for dropdown
const subcategoryOptions = computed(() => {
  const subcategories = new Set<string>();

  questions.value.forEach((question) => {
    if (question.subcategory) {
      subcategories.add(question.subcategory);
    }
  });

  return Array.from(subcategories);
});

// Get subcategory name by ID
function getSubcategoryName(subcategoryId: string): string | undefined {
  const subcategory = subcategories.value.find((s) => s.id === subcategoryId);
  return subcategory ? subcategory.name : 'Uncategorized';
}

// Variables available for AI prompts in questions
const categoryQuestionVariables = computed(() => {
  let baseVariables = [];
  filteredQuestions.value.forEach((question) => {
    baseVariables.push(question);
  });

  // Default variables if no specific category is matched
  return baseVariables;
});
</script>

<style scoped lang="scss">
.category-card {
  border-radius: $border-radius-lg;
  overflow: hidden;
  box-shadow: $card-shadow;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;

  .q-card-section {
    &.bg-primary {
      background: linear-gradient(
        135deg,
        $primary 0%,
        darken($primary, 10%) 100%
      );
    }
  }

  .category-list {
    .q-item {
      transition: all 0.2s ease;

      &.q-item--active {
        background-color: rgba($primary, 0.1);
        border-left: 3px solid $primary;
      }

      &:hover {
        background-color: rgba($primary, 0.05);
      }
    }
  }
  .subcategory-list {
    .q-item {
      transition: all 0.2s ease;

      &.q-item--active {
        background-color: rgba($amber, 0.1);
        border-left: 3px solid $amber;
      }

      &:hover {
        background-color: rgba($amber, 0.05);
      }
    }
  }
}

.category-list {
  max-height: 600px;
  overflow-y: auto;
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
}

.category-sidebar {
  border-right: 1px solid #f0f0f0;
}

.question-list {
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
}

.question-item {
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.02);
  }
}
.chip-padding {
  height: 24px;
  padding: 0 10px;
}

.no-category {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 400px;
}

.no-category-card {
  width: 100%;
  max-width: 400px;
}

.empty-state {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 2rem;
}

@media (min-width: 768px) {
  .category-list {
    height: calc(100vh - 200px);
    border-right: 1px solid #e5e7eb;
  }

  .category-details {
    height: calc(100vh - 200px);
    overflow-y: auto;
  }
}
</style>
