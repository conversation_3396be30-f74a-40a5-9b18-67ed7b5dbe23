<template>
  <section
    id="export"
    class="q-mx-auto"
    :class="{
      'q-pa-lg': !$q.screen.lt.sm, // Full width on xs and sm
    }"
  >
    <q-card
      :class="{
        'full-width': $q.screen.lt.sm, // Full width on xs and sm
      }"
    >
      <q-card-section class="flex justify-between">
        <h2 class="col-sm-12">Users</h2>
        <div class="q-pa-md">
          <div class="q-gutter-md row items-center">
            <q-input
              dense
              debounce="300"
              v-model="filter.search"
              placeholder="Search"
              :class="{
                'full-width': $q.screen.lt.sm, // Full width on xs and sm
              }"
            >
              <template v-slot:append>
                <q-icon name="search" />
              </template>
            </q-input>

            <q-select
              v-model="filter.role"
              :options="roleOptions"
              debounce="300"
              dense
              style="min-width: 200px"
              map-options
              emit-value
              :class="{
                'full-width': $q.screen.lt.sm, // Full width on xs and sm
              }"
            >
              <template v-slot:prepend>
                <q-icon name="fas fa-user-cog" @click.stop.prevent />
              </template>
              <template v-slot:append>
                <q-icon
                  name="close"
                  @click.stop.prevent="filter.role = ''"
                  class="cursor-pointer"
                />
              </template>
            </q-select>

            <q-select
              v-model="filter.status"
              :options="statusOptions"
              debounce="300"
              dense
              style="min-width: 200px"
              map-options
              emit-value
              :class="{
                'full-width': $q.screen.lt.sm, // Full width on xs and sm
              }"
            >
              <template v-slot:prepend>
                <q-icon name="person" @click.stop.prevent />
              </template>
              <template v-slot:append>
                <q-icon
                  name="close"
                  @click.stop.prevent="filter.status = ''"
                  class="cursor-pointer"
                />
              </template>
            </q-select>
          </div>
        </div>
      </q-card-section>
      <q-separator />
      <q-card-section class="q-pa-none">
        <q-table
          :dense="$q.screen.lt.md"
          class="my-sticky-header-table"
          flat
          bordered
          ref="tableRef"
          :rows="users"
          :columns="columns"
          row-key="uid"
          v-model:pagination="pagination"
          :filter="filter"
          binary-state-sort
          @request="onRequest"
        >
          <template v-slot:body-cell-admin="props">
            <q-td auto-width :props="props">
              <q-toggle
                v-model="props.row.isAdmin"
                @update:model-value="toggleAdmin(props.row)"
                :disable="loading"
              />
            </q-td>
          </template>

          <template v-slot:body-cell-reviewer="props">
            <q-td auto-width :props="props">
              <q-toggle
                v-model="props.row.isReviewer"
                @update:model-value="toggleReviewer(props.row)"
                :disable="loading"
              />
            </q-td>
          </template>

          <template v-slot:body-cell-paypal="props">
            <q-td auto-width :props="props">
              <q-toggle
                v-model="props.row.paidViaPaypal"
                @update:model-value="togglePaypal(props.row)"
                :disable="loading"
              />
            </q-td>
          </template>

          <template v-slot:body-cell-more="props">
            <q-td auto-width :props="props">
              <q-btn style="float: right" round flat icon="more_vert">
                <q-menu cover auto-close>
                  <q-list bordered separator>
                    <q-item
                      clickable
                      @click="moreActions(props.row)"
                      v-if="
                        props.row?.isAdmin === true ||
                        props.row?.isReviewer === true ||
                        props.row?.paidViaPaypal === true ||
                        props.row?.isOldAuthor === true ||
                        props.row?.stripeRole === 'basic'
                      "
                    >
                      <q-item-section avatar>
                        <q-icon name="fas fa-user" />
                      </q-item-section>
                      <q-item-section>Features</q-item-section>
                    </q-item>
                    <q-item clickable @click="reviewActions(props.row)">
                      <q-item-section avatar>
                        <q-icon name="open_in_new" />
                      </q-item-section>
                      <q-item-section>Login As</q-item-section>
                    </q-item>
                    <q-item
                      clickable
                      @click="generateResetPasswordLink(props.row)"
                    >
                      <q-item-section avatar>
                        <q-icon name="lock_reset" />
                      </q-item-section>
                      <q-item-section
                        >Generate reset password link</q-item-section
                      >
                    </q-item>
                  </q-list>
                </q-menu>
              </q-btn>
            </q-td>
          </template>
        </q-table>

        <q-inner-loading
          :showing="!users.length || loading"
          color="primary"
          label="Hang tight, Saving...."
          label-class="text-primary"
          label-style="font-size: 1.1em"
          style="z-index: 1"
        >
          <q-btn
            label="Load Users"
            icon="person_search"
            color="primary"
            @click="loadInitData"
            class="q-mb-md"
            v-if="!isUsersListLoaded"
            flat
            rounded
          >
            <q-tooltip
              >Click this icon to load the users initially, as the loading time
              may be slow.</q-tooltip
            >
          </q-btn>

          <q-spinner-dots v-if="isUsersListLoaded" color="primary" size="2em" />
          <div v-if="isUsersListLoaded" class="align-center">
            <q-icon name="img:robot.png" class="q-ml-xs" size="xl" />
            Hang tight, Loading....
          </div>
        </q-inner-loading>
      </q-card-section>
    </q-card>
    <q-dialog v-model="userMoreSettingsDialog">
      <q-card flat>
        <q-card-section class="q-pa-none">
          <q-bar class="bg-primary text-white">
            <q-icon name="img:robot.png" />

            <div>More Settings</div>

            <q-space />

            <q-btn dense flat icon="close" v-close-popup>
              <q-tooltip class="bg-white text-primary">Close</q-tooltip>
            </q-btn>
          </q-bar>
        </q-card-section>

        <q-card-section v-if="selectedRow.isActive">
          <!-- Allowed no. of books  -->
          <q-input
            v-model="selectedRow.allowed_books"
            clearable
            clear-icon="close"
            dense
            type="number"
            color="primary"
            label="Allowed Books"
          />
        </q-card-section>
        <q-card-section v-if="selectedRow.isActive">
          <div class="row justify-center items-center q-gutter-x-sm">
            <q-select
              v-model="selectedRow.assignedAdminId"
              :options="adminOptions"
              option-value="value"
              option-label="label"
              debounce="300"
              label="Select Reviewer"
              @filter="filterAdmins"
              @popup-hide="getAllUsers"
              dense
              map-options
              v-if="reviewers.length"
              style="min-width: 300px"
              @update:model-value="isReviewerChanged = true"
              hint="Click the mail icon to save. "
            >
              <template v-slot:append>
                <q-icon
                  name="close"
                  @click.stop.prevent="
                    unAssignReviewerFromAuthor(selectedRow.author)
                  "
                  class="cursor-pointer"
                />
              </template>
            </q-select>
            <q-btn
              icon="mail"
              color="primary"
              @click="sendEmailNotificationToReviewer"
              square
              dense
              :disable="
                !selectedRow.assignedAdminId ||
                (typeof selectedRow.assignedAdminId === 'object' &&
                  !selectedRow.assignedAdminId.value)
              "
              :loading="isSending"
            >
              <q-tooltip>Reviewer Assignment Notification</q-tooltip>
            </q-btn>
          </div>
        </q-card-section>
        <q-card-section
          v-if="selectedRow.toReview && selectedRow.toReview.length"
          class="q-py-xs"
        >
          <p class="q-py-xs q-ma-none"><strong>Assigned Authors: <AUTHORS>
          <q-separator />
          <div class="q-gutter-sm q-mt-xs">
            <q-chip
              v-for="authorToReview in selectedRow.toReview"
              removable
              v-model="authorToReview.id"
              @remove="unAssignReviewerFromAuthor(authorToReview)"
              color="primary"
              text-color="white"
              icon="person"
            >
              {{ authorToReview.name || authorToReview.email }}
            </q-chip>
          </div>
        </q-card-section>
        <q-card-section>
          <q-select
            v-model="selectedRow.features"
            :options="featureOptions"
            debounce="300"
            label="Select Features"
            dense
            style="min-width: 300px"
            map-options
            multiple
            use-chips
            emit-value
            v-if="1 == 2"
          >
            <template v-slot:prepend>
              <q-icon name="fas fa-layer-group" @click.stop.prevent />
            </template>
            <template v-slot:append>
              <q-icon
                name="close"
                @click.stop.prevent="selectedRow.features = []"
                class="cursor-pointer"
              />
            </template>
          </q-select>
        </q-card-section>
        <q-separator />
        <q-card-section class="q-pa-none">
          <q-card-actions align="right">
            <q-btn
              flat
              label="Save"
              icon="save"
              color="primary"
              @click="saveAuthorData"
              :disable="isSending || isReviewerChanged"
              v-if="selectedRow.isActive"
            />
            <q-btn
              flat
              label="Cancel"
              icon="cancel"
              color="danger"
              v-close-popup
              :disable="isSending"
            />
          </q-card-actions>
        </q-card-section>
      </q-card>
    </q-dialog>
  </section>
</template>

<script lang="ts" setup>
import {
  listUsers,
  setPaypalPaymentStatus,
  setUserAdminStatus,
  setUserReviewerStatus,
  type UserWithClaims,
} from 'src/shared/api/firebase-functions';
import { onMounted, ref } from 'vue';
import { type QTableProps, type QTable, useQuasar } from 'quasar';
import { confirmOverrideText } from 'src/shared/lib/quasar-dialogs';
import {
  isReviewMode,
  isUsersListLoaded,
  loginAs,
  user,
  useAuthorStore,
} from 'src/entities/user';
import { useRouter } from 'vue-router';
import { sendEmail, fetchResetPasswordLink } from 'src/entities/setting';
import { useNotificationStore } from 'src/entities/notification';

const router = useRouter();

// State to determine if the current mode is review
isReviewMode.value = '';
// State to store the current login role
loginAs.value = '';
// Reference to the QTable component
const tableRef = ref<QTable>();
// State to indicate if data is being sent
const isSending = ref(false);
// State to indicate if select reviewer has changed
const isReviewerChanged = ref(false);
// Reactive state for storing users
const users = ref<UserWithClaims[]>([]);
// Reactive state for storing reviewers
const reviewers = ref<UserWithClaims[]>([]);
// Reactive state for filters applied on the table
const filter = ref({ search: '', role: '', status: '' });
// Options for role filtering
const roleOptions = ref([
  { label: 'All', value: '' },
  { label: 'Admin', value: 'isAdmin' },
  { label: 'Reviewers', value: 'isReviewer' },
]);
// Options for status filtering
const statusOptions = ref([
  { label: 'All', value: '' },
  { label: 'Active', value: 'active' },
  { label: 'Inactive', value: 'inactive' },
]);
// State to indicate if data is loading
const loading = ref(true);
// Pagination state
const pagination = ref({
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
});

// Columns configuration for the table
type Columns = NonNullable<QTableProps['columns']>;
const columns: Columns = [
  {
    name: 'displayName',
    field: 'displayName',
    label: 'Name',
    align: 'left',
  },
  {
    name: 'email',
    field: 'email',
    label: 'Email',
    align: 'left',
  },
  {
    name: 'creationTime',
    field: 'creationTime',
    label: 'Created',
    align: 'left',
  },
  {
    name: 'lastSignInTime',
    field: 'lastSignInTime',
    label: 'Signed-In',
    align: 'left',
  },
  {
    name: 'stripe',
    field: 'stripeRole',
    label: 'Stripe Role',
    align: 'left',
  },
  {
    name: 'admin',
    field: 'isAdmin',
    label: 'Is admin?',
    align: 'center',
  },
  {
    name: 'paypal',
    field: 'paidViaPaypal',
    label: 'Paid via Paypal',
    align: 'center',
  },
  {
    name: 'reviewer',
    field: 'reviewer',
    label: 'Reviewer',
    align: 'center',
  },
  {
    name: 'more',
    field: 'more',
    label: '',
    align: 'center',
  },
];

// Author Store
const authorStore = useAuthorStore();
const notificationStore = useNotificationStore();

// Mapping of pagination tokens for Firebase integration
const pageTokens: Record<number, string | undefined> = {};
type QTableRequestParams = Parameters<NonNullable<QTableProps['onRequest']>>[0];
// Admin options for dropdowns or selections
const adminOptions = ref([]);
// Feature options derived from author features
const featureOptions = ref(authorStore.listAuthorFeatures());
// State for the currently selected row in the table
const selectedRow = ref({
  id: '',
  user: '',
  author: '',
  toReview: '',
  assignedAdminId: '',
  assignedAdminEmail: '',
  features: [],
  allowed_books: 4,
  isActive: true,
});
// State to control visibility of the 'more settings' dialog
const userMoreSettingsDialog = ref(false);

/**
 * Handles table request to load users based on pagination and filters.
 * @param props - The table request parameters including pagination and filters.
 */
const onRequest = async (props: QTableRequestParams) => {
  if (!isUsersListLoaded.value) return;

  const { page, rowsPerPage } = props.pagination;
  const filter: any = props.filter;
  loading.value = true;

  // Need the "next page" token of the previous page, and Quasar's `page` is 1-indexed
  const token = pageTokens[page - 1 - 1];
  const {
    users: newUsers,
    totalUsers,
    nextPageToken,
  } = await listUsers({
    filter: filter.search,
    filterByRole: filter.role,
    filterByStatus: filter.status,
    page: page,
    limit: rowsPerPage,
    ...(token && { pageToken: token }),
  });

  pageTokens[page - 1] = nextPageToken;

  // update rowsCount with appropriate value
  pagination.value.rowsNumber = totalUsers;

  users.value = newUsers;

  // don't forget to update local pagination object
  pagination.value.page = page;
  pagination.value.rowsPerPage = rowsPerPage;

  // ...and turn off loading indicator
  loading.value = false;
};

onMounted(async () => {
  // get initial data from server (1st page)
  if (isUsersListLoaded.value === true) await loadInitData();

  getAllUsers();
});

/**
 * Loads initial data for the component.
 */
const loadInitData = async () => {
  isUsersListLoaded.value = true;

  const { users: newReviewers } = await listUsers({
    filterByRole: 'isReviewer',
    limit: 50,
    page: 1,
  });
  reviewers.value = newReviewers;

  tableRef.value!.requestServerInteraction();
};
const $q = useQuasar();

/**
 * Toggles the admin status of a user.
 * @param user - The user object whose admin status is to be toggled.
 */
const toggleAdmin = async (user: UserWithClaims) => {
  const confirmed = await confirmOverrideText($q, {
    title: 'Set admin status',
    message: `Are you sure you want to ${
      user.isAdmin ? 'set' : 'remove'
    } the user ${user.displayName} (${user.email}) as an admin?`,
  });
  if (!confirmed) {
    user.isAdmin = !user.isAdmin;
    return;
  }
  loading.value = true;
  try {
    await setUserAdminStatus({
      uid: user.uid,
      isAdmin: user.isAdmin,
    });
    user.isAdmin = user.isAdmin;
  } catch (e) {
    $q.notify({
      message: `Failed to modify user admin status: ${JSON.stringify(
        e,
      )}. Please try again.`,
      color: 'red',
      timeout: 3000,
      icon: 'error',
    });
  } finally {
    loading.value = false;
  }
};

/**
 * Toggles the reviewer status of a user.
 * @param userSelected - The user object whose reviewer status is to be toggled.
 */
const toggleReviewer = async (userSelected: UserWithClaims) => {
  const confirmed = await confirmOverrideText($q, {
    title: 'Set reviewer status',
    message: `Are you sure you want to ${
      userSelected.isReviewer ? 'set' : 'remove'
    } the user ${userSelected.displayName} (${
      userSelected.email
    }) as a reviewer?`,
  });
  if (!confirmed) {
    userSelected.isReviewer = !userSelected.isReviewer;
    return;
  }
  loading.value = true;
  try {
    await setUserReviewerStatus({
      uid: userSelected.uid,
      isReviewer: userSelected.isReviewer,
    });
    userSelected.isReviewer = userSelected.isReviewer;
    if (!userSelected.isReviewer) {
      const userToRemove = reviewers.value.findIndex(
        (reviewer) => reviewer.uid === userSelected.uid,
      );
      delete reviewers.value[userToRemove];
    } else {
      if (user.value.uid !== userSelected.uid)
        reviewers.value.push(userSelected);
    }
  } catch (e) {
    $q.notify({
      message: `Failed to modify user reviewer status: ${JSON.stringify(
        e,
      )}. Please try again.`,
      color: 'red',
      timeout: 3000,
      icon: 'error',
    });
  } finally {
    loading.value = false;
  }
};

/**
 * Toggles the PayPal payment status of a user.
 * @param user - The user object whose PayPal payment status is to be toggled.
 */
const togglePaypal = async (user: UserWithClaims) => {
  const confirmed = await confirmOverrideText($q, {
    title: 'Set PayPal status',
    message: `Are you sure you want to ${
      user.paidViaPaypal ? 'mark' : 'unmark'
    } the user ${user.displayName} (${
      user.email
    }) as having paid through PayPal?`,
  });
  if (!confirmed) {
    user.paidViaPaypal = !user.paidViaPaypal;
    return;
  }
  loading.value = true;
  try {
    await setPaypalPaymentStatus({
      uid: user.uid,
      paidViaPaypal: user.paidViaPaypal,
    });
    user.paidViaPaypal = user.paidViaPaypal;
  } catch (e) {
    $q.notify({
      message: `Failed to modify user PayPal status: ${JSON.stringify(
        e,
      )}. Please try again.`,
      color: 'red',
      timeout: 3000,
      icon: 'error',
    });
  } finally {
    loading.value = false;
  }
};

/**
 * Opens the more actions dialog for a user.
 * @param user - The user object for which more actions are to be taken.
 */
const moreActions = async (user: UserWithClaims) => {
  const author = await authorStore.getAuthor(user.uid);
  selectedRow.value.user = user;
  selectedRow.value.author = author;
  selectedRow.value.id = user.uid;
  const assignedAdminId = author?.assignedAdminId || '';
  const assignedAdminEmail = author?.assignedAdminEmail || '';
  selectedRow.value.assignedAdminId = {
    value: assignedAdminId,
    label: assignedAdminEmail,
  };
  selectedRow.value.features = author?.features;
  selectedRow.value.allowed_books = author.allowed_books;
  const isActive =
    user?.isAdmin === true ||
    user?.paidViaPaypal === true ||
    user?.isOldAuthor === true ||
    user?.stripeRole === 'basic';
  selectedRow.value.isActive = isActive;
  if (user.isReviewer) {
    authorStore.listAuthorsForReview(user.uid);
    selectedRow.value.toReview = authorStore.authorsForReview;
  } else {
    selectedRow.value.toReview = null;
  }
  userMoreSettingsDialog.value = true;
};

/**
 * Initiates review actions for a user.
 * @param user - The user object for which review actions are to be initiated.
 */
const reviewActions = async (user: UserWithClaims) => {
  const confirmed = await confirmOverrideText($q, {
    title: 'Enter Review Mode',
    message: `Are you sure you want to review ${user.displayName}?`,
  });
  if (!confirmed) return;

  loginAs.value = user.uid;
  await router.push({ path: 'books' });
};

/**
 * Saves the author data after modifications.
 */
const saveAuthorData = async () => {
  userMoreSettingsDialog.value = false;
  let features = selectedRow.value.features;
  if (selectedRow.value.assignedAdminId.value) {
    features.push('review');
  } else {
    features = features.filter((feature) => feature !== 'review');
  }

  const newData = {
    id: selectedRow.value.id,
    assignedAdminId: selectedRow.value.assignedAdminId.value ?? '',
    assignedAdminEmail: selectedRow.value.assignedAdminId.label ?? '',
    features: features ?? [],
  };
  if (newData.assignedAdminId) {
    await authorStore.updateAuthor(selectedRow.value.id, newData);
    await authorStore.updateReviewerAuthorsToReview(
      newData.assignedAdminId,
      {
        email: selectedRow.value.user.email,
        id: selectedRow.value.id,
      },
      true,
    );

    await authorStore.updateAuthorAllowedBooksMeta(
      selectedRow.value.id,
      selectedRow.value.allowed_books || 4,
    );
  }
};

/**
 * Filters admins based on the input value.
 * @param val - The input value to filter admins.
 * @param update - The function to update the filtered results.
 */
const filterAdmins = (val: string, update: (callback: () => void) => void) => {
  if (val === '') {
    update(() => {
      getAllUsers();
    });
    return;
  }

  update(() => {
    const needle = val.toLowerCase();
    adminOptions.value = adminOptions.value.filter((v) => {
      return v.label.toLowerCase().indexOf(needle) > -1;
    });
  });
};

const generateResetPasswordLink = async (user: UserWithClaims) => {
  loading.value = true;

  try {
    loading.value = true;

    const resetPasswordLink = await fetchResetPasswordLink(user.uid);

    const message = resetPasswordLink
      ? `You can use the reset password link below: <br /> <a target="_blank" href="${resetPasswordLink}">Reset Password Link</a>. <br /><br />Please note that the link will expire in one hour.`
      : 'An error has occurred. Please try again!';

    $q.dialog({
      style: { width: '50vw' },
      title: 'Generate Reset Password Link',
      message: message,
      html: true,
      ok: {
        color: 'primary',
      },
    });
  } catch (error) {
    $q.dialog({
      style: { width: '50vw' },
      title: 'Error',
      message:
        'An error has occurred while generating the reset password link. Please try again!',
      ok: {
        color: 'primary',
      },
    });
  } finally {
    loading.value = false;
  }
};

/**
 * Retrieves all users and updates admin options.
 */
const getAllUsers = () => {
  let options = [];
  reviewers.value.forEach((reviewer) => {
    options.push({
      label: reviewer.email,
      value: reviewer.uid,
    });
  });
  adminOptions.value = options;
};

/**
 * Sends an email notification to the reviewer.
 */
const sendEmailNotificationToReviewer = () => {
  let subject =
    '[Manuscriptr] You Have Been Assigned as a Reviewer to ' +
      selectedRow.value?.user?.displayName || selectedRow.value?.user?.email;
  let message = `<p>Dear ${selectedRow.value?.assignedAdminId?.label},</p>
                     <p>We are pleased to inform you that you have been assigned the role of a <strong>Reviewer</strong> in Manuscriptr, associated with the author ${
                       selectedRow.value?.user?.displayName ||
                       selectedRow.value?.user?.email
                     }.</p>
            <p>Once logged in, you can view any available author books that require your review. If you've been assigned to multiple authors, you can select a specific author's book by using the <strong>Select Author</strong> dropdown menu in your dashboard.</p>
            <p>Thank you for your participation and valuable contributions to the Manuscriptr platform. We look forward to your insightful reviews.</p>
            <p>The Manuscriptr Team</p>`;

  if (!selectedRow.value?.assignedAdminId?.label) return;

  notificationStore.createUserNotification(
    selectedRow.value?.assignedAdminId?.value as string,
    {
      title: subject,
      notification: message,
    },
  );
  try {
    isSending.value = true;
    sendEmail({
      to: selectedRow.value.assignedAdminId.label,
      from: user?.value?.email,
      subject: subject,
      html: message,
    }).then(async (response) => {
      if (selectedRow.value.user.email) {
        subject =
          '[Manuscriptr] A Reviewer Has Been Assigned to Your Books: ' +
          selectedRow.value.assignedAdminId.label;
        message = `<p>Dear ${
          selectedRow.value.user.displayName || selectedRow.value.user.email
        },</p>
            <p>We are pleased to inform you that a <strong>Reviewer</strong> has been assigned to review your books on the Manuscriptr platform.</p>
            <p>The assigned reviewer is associated with the email address: ${
              selectedRow.value.assignedAdminId.label
            }.</p>
            <p>This step ensures that your work will receive the valuable attention it deserves. You can log in to your account to track the progress and updates from the assigned reviewer.</p>
            <p>If you have any questions or need assistance, please don't hesitate to reach out to our support team.</p>
            <p>The Manuscriptr Team</p>`;
        notificationStore.createUserNotification(
          selectedRow.value.user.uid as string,
          {
            title: subject,
            notification: message,
          },
        );
        await sendEmail({
          to: selectedRow.value.user.email,
          from: user?.value?.email,
          subject: subject,
          html: message,
        });
      }
      isSending.value = false;
      $q.notify({
        message: 'Message sent.',
        color: 'primary',
        timeout: 3000,
        icon: 'email',
      });
      await saveAuthorData();
    });
  } catch (err) {
    isSending.value = false;
    $q.notify({
      message: 'An error has occurred. Please try again.',
      color: 'red',
      timeout: 3000,
      icon: 'error',
    });
  }
};

/**
 * Unassigns a reviewer from an author.
 * @param user - The user object from which the reviewer is to be unassigned.
 */
const unAssignReviewerFromAuthor = async (user: any) => {
  const confirmed = await confirmOverrideText($q, {
    title: 'Unassigned Reviewer',
    message: `Are you sure you want to un-assign ${
      user.assignedAdminEmail
    } from the role of Reviewer for the user ${user.name || user.email}?`,
  });
  if (!confirmed) {
    return;
  }

  let subject =
    '[Manuscriptr] You Have Been Unassigned as a Reviewer from ' + user.name ||
    user.email;
  let message = `<p>Dear ${user.assignedAdminEmail},</p>
                   <p>We regret to inform you that you have been unassigned from the role of <strong>Reviewer</strong> in Manuscriptr for the author ${
                     user.name || user.email
                   }.</p>
          <p>Thank you for your past contributions to the Manuscriptr platform. If you have any questions or need further clarification, feel free to reach out to our support team.</p>
          <p>The Manuscriptr Team</p>`;

  if (!user.assignedAdminEmail) return;

  try {
    notificationStore.createUserNotification(user.assignedAdminId as string, {
      title: subject,
      notification: message,
    });
    await authorStore.updateReviewerAuthorsToReview(
      user.assignedAdminId,
      {
        email: selectedRow.value.user.email,
        id: selectedRow.value.id,
      },
      false,
    );

    isSending.value = true;
    sendEmail({
      to: user.assignedAdminEmail,
      from: user?.value?.email,
      subject: subject,
      html: message,
    })
      .then(async (response) => {
        if (user.email) {
          subject =
            '[Manuscriptr] A Reviewer Has Been Unassigned from Your Books: ' +
            user.assignedAdminEmail;
          message = `<p>Dear ${user.name || user.email},</p>
        <p>We would like to inform you that a <strong>Reviewer</strong> has been unassigned from reviewing your books on the Manuscriptr platform.</p>
        <p>The reviewer associated with the email address ${
          user.assignedAdminEmail
        } will no longer be reviewing your work.</p>
        <p>If you have any concerns or require further assistance, please don't hesitate to contact our support team.</p>
        <p>The Manuscriptr Team</p>`;

          notificationStore.createUserNotification(user.id as string, {
            title: subject,
            notification: message,
          });
          await sendEmail({
            to: user.email,
            from: user?.value?.email,
            subject: subject,
            html: message,
          });
        }
        isSending.value = false;
        $q.notify({
          message: 'Message sent.',
          color: 'primary',
          timeout: 3000,
          icon: 'email',
        });
        await authorStore.updateAuthor(user.id, {
          assignedAdminEmail: '',
          assignedAdminId: '',
        });
      })
      .finally(() => {
        if (!selectedRow.value.toReview) selectedRow.value.assignedAdminId = '';
      });
  } catch (err) {
    isSending.value = false;
    $q.notify({
      message: 'An error has occurred. Please try again.',
      color: 'red',
      timeout: 3000,
      icon: 'error',
    });
  }
};
</script>
<style lang="sass">
.my-sticky-header-table
  /* height or max-height is important */
  height: calc(100vh - 230px)

  .q-table__top,
  .q-table__bottom,
  thead tr:first-child th
    /* bg color is important for th; just specify one */
    background-color: white


  thead tr th
    position: sticky
    z-index: 1
  thead tr:first-child th
    top: 0

  /* this is when the loading indicator appears */
  &.q-table--loading thead tr:last-child th
    /* height of all previous header rows */
    top: 48px

  /* prevent scrolling behind sticky top row on focus */
  tbody
    /* height of all previous header rows */
    scroll-margin-top: 48px
</style>
