<template>
  <section
    id="export"
    class="q-mx-auto"
    :class="{
      'q-pa-lg': !$q.screen.lt.sm, // Full width on xs and sm
    }"
  >
    <q-card
      :class="{
        'full-width': $q.screen.lt.sm, // Full width on xs and sm
      }"
    >
      <q-card-section>
        <h2>Export</h2>
      </q-card-section>
      <q-separator />

      <q-card-section class="q-pa-none">
        <q-scroll-area
          :thumb-style="thumbStyle"
          :bar-style="barStyle"
          style="height: calc(100vh - 250px)"
        >
          <BookPreview :book="book" />
        </q-scroll-area>
      </q-card-section>
      <q-separator />
      <q-card-section>
        <q-card-actions
          :align="$q.screen.lt.sm ? 'center' : 'left'"
          class="q-gutter-sm"
        >
          <q-btn
            label="Export book"
            icon="note_add"
            color="primary"
            @click="exportBook"
          />
          <q-btn
            label="Export outline"
            icon="note_add"
            color="primary"
            @click="exportOutline"
          />
          <q-btn
            label="Export Images"
            color="primary"
            icon="photo_library"
            @click="exportImages"
          />
        </q-card-actions>
      </q-card-section>
    </q-card>
  </section>
</template>

<script setup lang="ts">
import { computed, unref } from 'vue';
import { saveAs } from 'file-saver';
import { useRoute } from 'vue-router';
import { getBook, getChapter, listOutlines } from 'src/entities/book';
import {
  BookPreview,
  bookHtmlToDocx,
  bookOutlineHtmlToDocx,
  exportBookImages,
} from 'src/features/book-export';
import { barStyle, thumbStyle } from 'src/entities/setting';

const route = useRoute();

const book = await getBook(route.params.id as string);
const bookOutlines = unref(await listOutlines(route.params.id as string));
const outlines = computed(() => [
  bookOutlines.introduction,
  ...bookOutlines.outlines?.filter((chapter) => !chapter.isSection),
  bookOutlines.conclusion,
]);

async function exportBook() {
  const chapters = await Promise.all(
    outlines.value.map((outline) => getChapter(book.value.id, outline.id)),
  );
  bookHtmlToDocx(book.value, outlines.value, chapters.map(unref));
}

function exportOutline() {
  bookOutlineHtmlToDocx(book.value, outlines.value);
}

async function exportImages() {
  await exportBookImages(book.value, bookOutlines);
}
</script>
