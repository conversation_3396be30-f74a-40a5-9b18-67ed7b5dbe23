<template>
  <section
    id="export"
    class="q-mx-auto"
    :class="{
      'q-pa-lg': !$q.screen.lt.sm, // Full width on xs and sm
    }"
  >
    <q-card
      :class="{
        'full-width': $q.screen.lt.sm, // Full width on xs and sm
      }"
    >
      <q-card-section>
        <div class="row">
          <div class="col-4 col-md-4 col-xs-12">
            <UserInfoCard :user="user" />
          </div>
          <div class="col col-lg-8 col-md-8 col-sm-12 col-xs-12">
            <q-list>
              <q-item>
                <q-item-section>
                  <q-item-label overline
                    ><h2 class="text-black">Subscriptions</h2></q-item-label
                  >
                </q-item-section>

                <q-item-section side top>
                  <q-icon name="verified" color="primary" />
                </q-item-section>
              </q-item>

              <q-separator spaced />

              <q-item class="q-pa-none">
                <q-item-section v-if="isLoadingSubs">
                  <q-skeleton type="text" />
                  <q-skeleton type="text" class="text-subtitle1" />
                  <q-skeleton type="text" width="100%" class="text-subtitle1" />
                  <q-skeleton type="text" width="80%" class="text-subtitle1" />
                  <q-skeleton type="text" class="text-caption" />
                </q-item-section>
                <q-item-section v-else-if="errorSubs">
                  An error has occurred: {{ errorSubs }}
                </q-item-section>
                <q-item-section
                  v-else-if="subs.length"
                  v-for="sub in subs"
                  :key="sub.id"
                >
                  <SubscriptionCard
                    :subscription="sub"
                    @cancelled="cancelled"
                  />
                </q-item-section>
                <q-item-section v-else>
                  <UnsubscribedNotice
                    message="You no longer have an active subscription"
                  />
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </div>
      </q-card-section>
      <q-separator />

      <q-card-actions align="center">
        <div>
          <q-btn
            flat
            color="danger"
            icon="delete"
            label="Delete Account"
            @click="requestAccountDeletion"
            :loading="loading"
            v-if="
              !userLoggedIn?.accountDeletionRequestedAt &&
              !userLoggedIn?.accountDeletionScheduledDate
            "
          />
          <q-btn
            flat
            color="danger"
            icon="cancel"
            label="Cancel Account Deletion"
            @click="cancelAccountDeletion"
            :loading="loading"
            v-else
          />
        </div>
        <q-btn flat color="purple" icon="help" label="Help" to="/help" />
        <q-btn flat color="primary" icon="home" label="Books" to="/books" />
      </q-card-actions>
    </q-card>
  </section>
</template>

<script setup lang="ts">
import { useAsyncState, watchDebounced } from '@vueuse/core';
import { getMyProfileSubscriptions } from 'src/entities/package';
import SubscriptionCard from 'src/entities/package/ui/SubscriptionCard.vue';
import UnsubscribedNotice from 'src/entities/package/ui/UnsubscribedNotice.vue';
import {
  UserInfoCard,
  user,
  userLoggedIn,
  useAuthorStore,
  useSubscriptionStore,
} from 'src/entities/user';
import { confirmOverrideText } from 'src/shared/lib/quasar-dialogs';
import { ref } from 'vue';
import { useQuasar } from 'quasar';
import { sendEmail } from 'src/entities/setting';
import { useNotificationStore } from 'src/entities/notification';
import { ACTIONS, loggingService, PAGES } from 'src/entities/log';

// State management for subscriptions
const {
  state: subs,
  isLoading: isLoadingSubs,
  error: errorSubs,
} = useAsyncState(getMyProfileSubscriptions(), []);

// Quasar framework hook
const $q = useQuasar();
const loading = ref(false);
const subscriptionStore = useSubscriptionStore();
const authorStore = useAuthorStore();
const notificationStore = useNotificationStore();

// Watch for changes in newSubscription and update subscriptions list debounced
watchDebounced(
  subscriptionStore,
  async (newVal) => {
    if (newVal.isSubscribe === false || newVal.newSubscription) {
      isLoadingSubs.value = true;
      subs.value = await getMyProfileSubscriptions();
      isLoadingSubs.value = false;
    }
  },
  { debounce: 3500, maxWait: 10000 },
);

/**
 * Request the deletion of the user account.
 */
const requestAccountDeletion = async (): Promise<void> => {
  const confirmed = await confirmOverrideText($q, {
    title: 'Confirm Account Deletion Request',
    message:
      'Are you sure you want to request account deletion? This action cannot be undone once processed.',
  });
  if (!confirmed) {
    return;
  }
  loading.value = true;

  try {
    await authorStore.updateAuthor(user.value?.uid as string, {
      accountDeletionRequestedAt: Date.now(),
    });
    await loggingService.logAction(
      PAGES.PROFILE,
      ACTIONS.DELETE,
      `Requested Account Deletion`,
      'profile',
      user.value?.uid,
    );
    $q.notify({
      message:
        'Your account deletion request has been submitted successfully. You will be notified once it is processed.',
      color: 'primary',
      timeout: 3000,
      icon: 'check',
    });
  } catch (e) {
    $q.notify({
      message:
        'There was an issue submitting your request. Please try again later.',
      color: 'red',
      timeout: 3000,
      icon: 'error',
    });
  } finally {
    loading.value = false;

    notificationStore.createUserNotification(user?.value?.uid as string, {
      title: `Account Deletion Request `,
      notification: `<p>We have received your request to delete your Manuscriptr account. We are processing your request in accordance with our Deletion Policy.</p>`,
      url: `/profile`,
    });
  }
};

/**
 * Cancel the account deletion request.
 */
const cancelAccountDeletion = async (): Promise<void> => {
  const confirmed = await confirmOverrideText($q, {
    title: 'Cancel Account Deletion Request',
    message:
      'Are you sure you want to cancel your account deletion request? Your account will remain active, and the deletion process will be halted.',
  });
  if (!confirmed) {
    return;
  }
  loading.value = true;

  try {
    await authorStore.updateAuthor(user.value?.uid as string, {
      accountDeletionRequestedAt: null,
      accountDeletionScheduledDate: null,
    });
    await loggingService.logAction(
      PAGES.PROFILE,
      ACTIONS.DELETE,
      `Account Deletion Cancelled`,
      'profile',
      user.value?.uid,
    );
    const subject = 'Account Deletion Request Cancellation';
    const message = `<p>Dear ${user.value?.displayName || user.value.email},</p>
          <p>
          We wanted to inform you that your request to cancel the account deletion has been successfully processed. Your account remains active, and no further action will be taken regarding the deletion.
          </p>
          <p>
          If you did not initiate this cancellation, please contact our support team immediately.
          </p>
          <p>The Manuscriptr Team</p>`;

    if (!user.value?.email) return;

    await sendEmail({
      to: user.value?.email,
      from: user?.value?.email,
      cc: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      subject: subject,
      html: message,
    });
    $q.notify({
      message: 'Your account deletion request has been successfully canceled.',
      color: 'primary',
      timeout: 3000,
      icon: 'check',
    });
  } catch (e) {
    console.log(e);
    $q.notify({
      message:
        'There was an issue submitting your request. Please try again later.',
      color: 'red',
      timeout: 3000,
      icon: 'error',
    });
  } finally {
    loading.value = false;

    notificationStore.createUserNotification(user?.value?.uid as string, {
      title: `Cancel Account Deletion Request `,
      notification: `<p>We wanted to inform you that your request to cancel the account deletion has been successfully processed. Your account remains active, and no further action will be taken regarding the deletion.</p>`,
      url: `/profile`,
    });
  }
};

const cancelled = () => {
  isLoadingSubs.value = true;
  subs.value = [];
  isLoadingSubs.value = false;
};
</script>
