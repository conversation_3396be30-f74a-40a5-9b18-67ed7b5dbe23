<template>
  <q-drawer
    v-model="drawerRight"
    side="right"
    bordered
    :width="chapterMaxWidth + 46"
    :breakpoint="300"
  >
    <div class="q-pa-sm">
      <RightDrawer
        v-model:selectedNav="selectedNav"
        v-model:drawerRight="drawerRight"
        v-model:splitterModel="splitterModel"
        :selectedPrompt="selectedPrompt"
        :selectedPromptText="selectedPromptText"
        v-model:outlines="outlines"
        v-model:selectedChapterOutline="selectedChapterOutline"
        :is-mobile-view="browserWidth <= maxSideNavWidth"
        :chapter-max-width="chapterMaxWidth"
        :is-in-version-preview="isInVersionPreview"
        v-model:editing-dialog-active="editingDialogActive"
        v-model:book="book"
        v-model:is-loading-chapter="isLoadingChapter"
        :selected-chapter-id="selectedChapterId"
      />
    </div>
  </q-drawer>

  <q-splitter
    class="book-editor"
    v-model="splitterModel"
    disable
    unit="px"
    :limits="[10, Infinity]"
    separator-style="z-index: 1"
  >
    <template v-slot:separator>
      <q-btn
        round
        color="primary"
        size="xs"
        :icon="splitterModel > 10 ? 'chevron_left' : 'chevron_right'"
        @click="toggleSplitter"
      />
    </template>

    <template v-slot:before>
      <div id="left-panel">
        <q-toolbar class="relative-position q-px-none">
          <h1 class="q-px-lg q-pt-md text-center">
            {{ book.title }}
          </h1>
        </q-toolbar>
        <Chapters
          class="q-pa-md"
          :readonly="isInVersionPreview"
          v-model:book="book"
          v-model:outlines="outlines"
          v-model:selectedChapterId="selectedChapterId"
          v-model:isLoadingChapter="isLoadingChapter"
          v-model:chapterMaxWidth="chapterMaxWidth"
          @toggleDrawer="
            selectedNav = 'outline';
            drawerRight = true;
          "
        />
      </div>
    </template>

    <template v-slot:after>
      <div class="flex items-start justify-center q-pa-md">
        <div
          id="write"
          class="container"
          style="width: 100%"
          v-if="selectedChapterId != null"
        >
          <div id="editor-container">
            <div id="toolbar"></div>
            <editor
              id="editor"
              v-model="editorContent"
              api-key="yk2zcr92ljrjqpxrxbfo8p5914oyhcka9eowu2gohwtzuoro"
              :disabled="isInVersionPreview"
              :init="editorInit"
            />
            <q-inner-loading
              :showing="isLoadingChapter"
              color="primary"
              label="Hang tight, Saving...."
              label-class="text-primary"
              label-style="font-size: 1.1em"
              style="z-index: 1"
            >
              <q-spinner-dots color="primary" size="2em" />
              <div class="align-center">
                <q-icon name="img:robot.png" class="q-ml-xs" size="xl" />
                Hang tight, Loading....
              </div>
            </q-inner-loading>
          </div>
        </div>
      </div>
    </template>
  </q-splitter>
  <q-dialog v-model="editingDialogActive" persistent>
    <q-card
      class="row no-wrap"
      :style="{
        minWidth:
          browserWidth <= maxSideNavWidth
            ? browserWidth + 'px'
            : 'min(70vw, 1024px)',
        maxWidth:
          browserWidth <= maxSideNavWidth ? browserWidth + 'px' : '768px',
      }"
      style="width: 100%; height: 90%"
    >
      <q-btn
        icon="close"
        flat
        style="height: 72px"
        @click="editingDialogActive = false"
        class="absolute-top-right all-pointer-events"
      />

      <NewBookDialog
        :initial-values="book"
        :initial-outlines="outlines.outlines"
        @updated="(id, book) => updateCurrentBook(book)"
      />
      <div style="width: 56px" />
    </q-card>
  </q-dialog>
  <Teleport to="#book-actions" v-if="browserWidth > maxSideNavWidth">
    <q-btn
      flat
      @click="editingDialogActive = true"
      :disable="isInVersionPreview"
    >
      Book Foundations
    </q-btn>
  </Teleport>
  <Teleport to="#review-status">
    <q-btn
      v-if="isInVersionPreview"
      class="q-mr-lg text-negative header-mode-boxes"
      to="/books"
    >
      EXIT REVIEW MODE
    </q-btn>
  </Teleport>
  <Teleport to="#right-drawer">
    <q-btn
      icon="menu_book"
      dense
      flat
      round
      color="white"
      @click="toggleDrawer"
    >
      <q-tooltip>Toggle right drawer</q-tooltip>
    </q-btn>
  </Teleport>
  <Teleport to="#header-title">
    {{
      browserWidth <= 540
        ? trimText(book.title || 'Untitled book', 15)
        : book.title || 'Untitled book'
    }}
    <q-tooltip>{{ book.title }}</q-tooltip>
  </Teleport>
</template>

<script lang="ts" setup>
import {
  ref,
  computed,
  watch,
  onMounted,
  toValue,
  type Ref,
  unref,
  onUnmounted,
} from 'vue';
import { useRoute } from 'vue-router';
import { watchIgnorable } from '@vueuse/core';
import type TinyMCE from 'tinymce';
import type { EditorOptions } from 'tinymce';
import Editor from '@tinymce/tinymce-vue';
import NewBookDialog from 'src/entities/book/ui/components/onboarding/nonfiction/NewBookFlow.vue';
import { warn } from 'src/shared/lib/quasar-dialogs';
import {
  registerSpeechToTextPlugin,
  createFootnotePlugin,
  registerMannyButtonsPlugin,
  createImagePlugin,
  createBookWordCountPlugin,
  generateChapterInitialContent,
  createSaveVersionPlugin,
  createExportToWordPlugin,
  bookChapterSelect,
  mannyIconSVG,
  createMissionStatementPlugin,
  createExportBookImagesPlugin,
  createPageSetupPlugin,
  createBookPrinterPlugin,
} from 'src/features/tinymce-plugins';
import {
  getBook,
  getChapter,
  type Book,
  type Chapter,
  updateOutline,
  listOutlines,
  updateBook,
  BookOutlines,
  setOutlineWordCount,
  RightDrawer,
  Chapters,
} from 'src/entities/book';
import { event, useQuasar } from 'quasar';
import {
  createBookVersion,
  getBookVersion,
  getVersionChapter,
  listVersionOutlines,
} from 'src/features/book-versioning';
import { getTodayFormattedDateTime } from 'src/features/book-prompts';
import { canPrintBook, isMaintenanceMode } from 'src/entities/setting';

declare const tinymce: typeof TinyMCE;

const route = useRoute();

const bookId = route.params.id as string;
const versionId = route.query.version as string | null;
const selectedNav = ref('outline');
const selectedPrompt = ref('');
const selectedPromptText = ref('');

const isInVersionPreview = computed(() => versionId != null);

const book: Ref<Book> = versionId
  ? await getBookVersion(bookId, versionId)
  : await getBook(bookId);
const outlines: Ref<BookOutlines> = versionId
  ? await listVersionOutlines(bookId, versionId)
  : await listOutlines(bookId);

const drawerRight = ref(false);
const toggleDrawer = () => {
  book.value.drawer.right = !drawerRight.value;
  drawerRight.value = !drawerRight.value;
};

// Filter out outlines with isSection === true
if (outlines.value.outlines) {
  outlines.value.outlines = outlines.value?.outlines.filter(
    (outline) => !outline.isSection,
  );
}

const sideNavWidth = 365;
const sideNavChaptersWidth = 330;
const maxSideNavWidth = 767;
const remainSideWIdth = 10;
const minusPaddingSiveNavWidth = 32;
const minusShowToggleButton = 25;
// Reactive references for browser width and div size
const browserWidth = ref(window.innerWidth - minusShowToggleButton);
const isSmallerScreen = computed(() => browserWidth.value <= maxSideNavWidth);
const splitterModel = ref(
  isSmallerScreen.value ? remainSideWIdth : sideNavWidth,
);
const chapterMaxWidth = ref(
  isSmallerScreen.value
    ? browserWidth.value - minusPaddingSiveNavWidth
    : sideNavChaptersWidth,
);

// Function to update the browser width
const updateWidth = () => {
  browserWidth.value = window.innerWidth - minusShowToggleButton;
  splitterModel.value = isSmallerScreen.value ? remainSideWIdth : sideNavWidth;
  chapterMaxWidth.value = isSmallerScreen.value
    ? browserWidth.value - minusPaddingSiveNavWidth
    : sideNavChaptersWidth;
};
const initBook = () => {
  if (!book.value?.pageSetup) {
    book.value.pageSetup = {
      selectbox: 'default',
      marginleft: 1,
      marginright: 1,
      margintop: 1,
      marginbottom: 1,
      style: '',
    };
  }

  if (!book.value?.drawer) {
    book.value.drawer = {
      left: splitterModel.value,
      right: false,
    };
  }

  if (!isSmallerScreen.value) {
    splitterModel.value = book.value.drawer.left;
    drawerRight.value = book.value.drawer.right;
  }
};
onMounted(() => {
  // Update browser width on resize
  window.addEventListener('resize', updateWidth);
  initBook();
  // @ts-expect-error Until EventBus is removed
  window.EventBus.$on('insert-image', () => {
    // sideView.value = 'images';
  });
});

onUnmounted(() => {
  // Clean up event listeners and observers
  window.removeEventListener('resize', updateWidth);
});

// toggleSplitter
const toggleSplitter = () => {
  if (splitterModel.value > remainSideWIdth)
    splitterModel.value = remainSideWIdth;
  else
    splitterModel.value = isSmallerScreen.value
      ? browserWidth.value
      : sideNavWidth;

  book.value.drawer.left = splitterModel.value;
};
const selectedChapterId = ref('introduction');
const isLoadingChapter = ref(true);
const selectedChapter: Ref<Chapter> = versionId
  ? ref(await getVersionChapter(bookId, versionId, selectedChapterId.value))
  : await getChapter(bookId, selectedChapterId.value);

// using a manual `watch` because `computedAsync` causes the initial value to be undefined
isLoadingChapter.value = false;
window.dictation = '';
watch(selectedChapterId, async (chapterId) => {
  isLoadingChapter.value = true;

  if (window.dictation) {
    selectedChapterId.value = window.dictation;
    $q.notify({
      badgeStyle: 'opacity: 0',
      message:
        'Please remember to disable dictation before moving on to the next chapter.',
    });
  }

  selectedChapter.value = versionId
    ? await getVersionChapter(bookId, versionId, selectedChapterId.value)
    : toValue(await getChapter(bookId, selectedChapterId.value));

  isLoadingChapter.value = false;
});

const totalWordCount = computed(() => {
  return (
    outlines.value.introduction.wordCount +
    outlines.value.conclusion.wordCount +
    outlines.value.outlines.reduce((acc, ch) => ch.wordCount + acc, 0)
  );
});
const selectedChapterOutlineTitle = computed(() => {
  let title = selectedChapterOutline.value.title;
  if (!title) {
    if (selectedChapterId.value === 'introduction') {
      title = 'Introduction';
    } else if (selectedChapterId.value === 'conclusion') {
      title = 'Conclusion';
    } else {
      const chapterIdx = outlines.value.outlines.findIndex(
        (ch) => selectedChapterId.value === ch.id,
      );
      if (chapterIdx !== -1) {
        title = `Chapter ${chapterIdx + 1}`;
      }
    }
  }

  return isSmallerScreen.value ? trimText(title, 15) : title;
});
const trimText = (text: string, maxLength: number) => {
  if (text.length <= maxLength) {
    return text;
  }
  return text.slice(0, maxLength) + '...';
};

const chapterOptions = computed(() => [
  {
    label: 'Introduction',
    value: 'introduction',
  },
  ...outlines.value.outlines?.map((chapter, idx) => ({
    label:
      'Chapter ' + (idx + 1) + (chapter.title ? ` | ${chapter.title}` : ''),
    value: chapter.id,
  })),
  {
    label: 'Conclusion',
    value: 'conclusion',
  },
]);

const $q = useQuasar();

// This property is to preserve reactivity when referencing a chapter outline (on the book doc)
const selectedChapterOutline = computed({
  get() {
    if (selectedChapterId.value === 'introduction') {
      return outlines.value.introduction;
    } else if (selectedChapterId.value === 'conclusion') {
      return outlines.value.conclusion;
    }
    const idx = outlines.value.outlines?.findIndex(
      (ch) => ch.id === selectedChapterId.value,
    );
    if (idx !== -1 && outlines.value.outlines) {
      return outlines.value.outlines[idx];
    }
  },
  set(value) {
    if (selectedChapterId.value === 'introduction') {
      outlines.value.introduction = value;
    } else if (selectedChapterId.value === 'conclusion') {
      outlines.value.conclusion = value;
    } else {
      const idx = outlines.value.outlines?.findIndex(
        (ch) => ch.id === selectedChapterId.value,
      );
      if (idx !== -1 && outlines.value.outlines) {
        outlines.value.outlines[idx] = value;
      }
    }
  },
});

// When using `computed`, TinyMCE would have a weird refresh placing the cursor in the very beginning
// so a manual `watch` is needed here.
const editorContent = ref('');
watch(
  selectedChapter,
  (chapter, oldChapter) => {
    window.selectedChapterId = selectedChapterOutline.value.id;
    window.selectedChapter =
      selectedChapterOutline.value?.title ?? selectedChapterOutline.value?.id;

    if (chapter?.id === oldChapter?.id) {
      // Only care about switching chapters
      return;
    }
    const escapedTitle = selectedChapterOutline.value.title.replace(
      /&/g,
      '&amp;',
    );

    // adds chapter title to Intro and Conclusion
    if (!selectedChapterOutline.value.title) {
      if (selectedChapterId.value === 'introduction') {
        selectedChapterOutline.value.title = 'Introduction';
      } else if (selectedChapterId.value === 'conclusion') {
        selectedChapterOutline.value.title = 'Conclusion';
      }
    }
    if (selectedChapterOutline.value.title) {
      // Create a regular expression to match all <h1> elements and  Remove all existing <h1> elements with the specified title from the chapter content
      const regex = new RegExp(`<h1(?:\\s+[^>]+)?>${escapedTitle}</h1>`, 'gi');
      const chapterContent = chapter.content.replace(regex, '');
      editorContent.value = `<h1 class="chapter-title">${selectedChapterOutline.value.title}</h1> ${chapterContent}`;
    } else {
      editorContent.value = chapter.content;
    }
  },
  { immediate: true },
);

// Title changed in the outline
const { ignoreUpdates: ignoreTitleUpdates } = watchIgnorable(
  () => selectedChapterOutline.value?.title,
  (title, oldTitle) => {
    editorContent.value =
      (title ? `<h1 class="chapter-title">${title}</h1>\n` : '') +
      selectedChapter.value?.content;
  },
);
const parser = new DOMParser();
watch(editorContent, (content) => {
  try {
    const doc = parser.parseFromString(content, 'text/html');

    // const title = doc.querySelector('.chapter-title');
    const title = doc.querySelector('h1');
    if (title) {
      if (selectedChapterOutline.value.title !== title.textContent) {
        ignoreTitleUpdates(() => {
          selectedChapterOutline.value.title = title.textContent ?? '';
          updateOutline(
            book.value.id,
            selectedChapterOutline.value.id,
            selectedChapterOutline.value,
          );
        });
      }
      title.remove();
    } else if (selectedChapterOutline.value.title !== '') {
      ignoreTitleUpdates(() => {
        selectedChapterOutline.value.title = '';
        updateOutline(
          book.value.id,
          selectedChapterOutline.value.id,
          selectedChapterOutline.value,
        );
      });
    }

    selectedChapter.value.content = doc.body.innerHTML;
    outlines.value = setOutlineWordCount(
      book.value.id,
      selectedChapterOutline.value.title,
      outlines,
      selectedChapterOutline.value.id,
      selectedChapter.value.content,
    );
  } catch (e) {
    console.log(e);
  }
});

const pageSetupSetting = computed(() => book.value?.pageSetup);
const editingDialogActive = ref(false);
async function updateCurrentBook(newData: Book) {
  if (!newData.id) {
    newData.id = bookId;
  }
  await updateBook(newData.id, newData);
  book.value = newData;
  editingDialogActive.value = false;
  $q.notify('Book updated successfully!');
  outlines.value = unref(await listOutlines(newData.id));
  // Filter out outlines with isSection === true
  if (outlines.value.outlines) {
    outlines.value.outlines = outlines.value?.outlines.filter(
      (outline) => !outline.isSection,
    );
  }
}
const defaultToolbarButtons =
  'undo redo | bookchapterselect | blocks | fontsize | manny | bold italic underline forecolor | dictate | link image-insert';
const moreButtons =
  'bullist numlist | removeformat | searchreplace | alignleft aligncenter alignright alignjustify';
const mannyButtons = computed(() => {
  let defaultBtns =
    'generatechaptercontentbtn | saveversionbtn | exporttowordbtn | exportbookimagesbtn | bookpagesetupbtn | bookprinterbtn';
  if (!canPrintBook.value) {
    defaultBtns = removeString('bookprinterbtn', defaultBtns);
  }
  return defaultBtns;
});
const mannyFileButtons = computed(() => {
  let defaultBtns =
    'saveversion | exporttoword | exportbookimages | missionstatement | bookprinter';
  if (!canPrintBook.value) {
    defaultBtns = removeString('bookprinter', defaultBtns);
  }
  return defaultBtns;
});
const removeString = (toRemove: string, hayStack: string) => {
  return hayStack
    .replace(new RegExp(`\\s*\\|\\s*${toRemove}\\s*`, 'g'), '')
    .replace(/\s*\|\s*$/, ''); // To handle trailing separator
};
const toolbarButtons = isSmallerScreen.value
  ? `${defaultToolbarButtons} | ${mannyButtons.value} | ${moreButtons}`
  : `${defaultToolbarButtons} | more`;
const editorInit: Partial<EditorOptions> = {
  height: 'calc(100vh - 90px)',
  menubar: 'file edit view insert format tools',
  menu: {
    file: {
      title: 'File',
      items: mannyFileButtons.value,
    },
    edit: {
      title: 'Edit',
      items: 'undo redo | cut copy paste pastetext | selectall | searchreplace',
    },
    insert: {
      title: 'Insert',
      items:
        'image link media template codesample inserttable | charmap hr pagebreak nonbreaking anchor toc | insertdatetime',
    },
    format: {
      title: 'Format',
      items:
        'bold italic underline strikethrough superscript subscript codeformat | blockquote | forecolor backcolor | removeformat',
    },
    tools: {
      title: 'Tools',
      items: 'wordcount | bookpagesetup',
    },
  },
  // skin: 'tinymce-5',
  plugins: [
    'book-printer',
    'book-page-setup',
    'export-book-images',
    'mission-statement',
    'book-chapter-select',
    'generate-chapter-content',
    'export-to-word',
    'book-word-count',
    'save-version',
    'wordcount',
    'advlist',
    'autolink',
    'lists',
    'link',
    'searchreplace',
    'nonbreaking',
    'quickbars',
    // 'custom-comment',
    'custom-footnote',
    'custom-image',
    'manny-buttons',
    'speech-to-text',
    'table',
  ],
  toolbar_mode: 'floating',
  toolbar: toolbarButtons,
  formats: {
    underline: { inline: 'u' },
  },

  block_formats:
    'Paragraph=p; Chapter Title=h1; Chapter Section=h2; Chapter Subsection=h3',
  fontsize_formats:
    '12=12px 14=14px 16=16px 18=18px 24=24px 32=32px 36=36px 48=48px',
  advlist_bullet_styles: 'disc',
  advlist_number_styles:
    'default,upper-alpha,upper-roman,lower-alpha,lower-roman',
  nonbreaking_force_tab: true,
  nonbreaking_wrap: true,
  statusbar: true,
  paste_as_text: true,
  paste_block_drop: true,
  quickbars_selection_toolbar: 'rewrite simplify write add-story edit',
  quickbars_insert_toolbar: '',
  contextmenu: 'undo redo | blocks | selectall | searchreplace',
  inline: false,
  fixed_toolbar_container: '#toolbar',
  browser_spellcheck: true,
  elementpath: false,
  content_css: '/tinymceStyles.css',
  init_instance_callback: (editor) => {
    editor.focus();
    isLoadingChapter.value = false;
  },
  setup: (editor) => {
    isLoadingChapter.value = true;
    // group more icons
    editor.ui.registry.addGroupToolbarButton('more', {
      icon: 'more-drawer',
      tooltip: 'More Options',
      items: moreButtons,
    });
    editor.ui.registry.addIcon('mannyicon', mannyIconSVG);
    editor.ui.registry.addGroupToolbarButton('manny', {
      icon: 'mannyicon',
      tooltip: 'Generate Content using Manny',
      items: 'generatechaptercontent',
    });

    const isChrome = !!window.chrome;
    if (isChrome) {
      registerSpeechToTextPlugin(tinymce, () => {
        // Trigger reactivity (since this doesn't update modelValue)
        selectedChapter.value.content = editor.getContent();
      });
    }
    createPageSetupPlugin(
      tinymce,
      pageSetupSetting,
      isSmallerScreen,
      () => {
        //isLoadingChapter.value = false;

        if (window.bookPageSetup) book.value.pageSetup = window.bookPageSetup;
      },
      () => {
        // isLoadingChapter.value = true;
      },
    );
    createBookWordCountPlugin(
      tinymce,
      totalWordCount,
      selectedChapterOutlineTitle,
    );
    createMissionStatementPlugin(tinymce, () => {
      editingDialogActive.value = true;
    });
    generateChapterInitialContent(
      tinymce,
      book.value,
      selectedChapterId.value,
      selectedChapterOutline.value.title,
      async () => {
        isLoadingChapter.value = true;
        if (!book.value.title || !book.value.subtitle) {
          warn($q, {
            message: 'Book title and subtitle are required',
          });
          isLoadingChapter.value = false;
          return false;
        } else if (
          !['introduction', 'conclusion'].includes(selectedChapterId.value) &&
          !selectedChapterOutline.value.title
        ) {
          warn($q, {
            message: 'A chapter name is required first',
          });
          isLoadingChapter.value = false;
          return false;
        } else {
          return true;
        }
      },
      () => {
        isLoadingChapter.value = false;
        const response = editor.getContent();
        if (response) {
          // adds chapter title to Intro and Conclusion
          if (!selectedChapterOutline.value.title) {
            if (selectedChapterId.value === 'introduction') {
              selectedChapterOutline.value.title = 'Introduction';
            } else if (selectedChapterId.value === 'conclusion') {
              selectedChapterOutline.value.title = 'Conclusion';
            }
          }
          selectedChapter.value.content = response;
          if (selectedChapterOutline.value.title) {
            const escapedTitle = selectedChapterOutline.value.title.replace(
              /&/g,
              '&amp;',
            );
            // Create a regular expression to match all <h1> elements and  Remove all existing <h1> elements with the specified title from the chapter content
            const regex = new RegExp(
              `<h1(?:\\s+[^>]+)?>${escapedTitle}</h1>`,
              'gi',
            );
            const chapterContent = response.replace(regex, '');
            selectedChapter.value.content = `<h1 class="chapter-title">${selectedChapterOutline.value.title}</h1> ${chapterContent}`;
          }
          editorContent.value = selectedChapter.value.content;
        }
      },
    );
    createSaveVersionPlugin(
      tinymce,
      bookId,
      () => {
        isLoadingChapter.value = false;
      },
      () => {
        isLoadingChapter.value = true;
      },
    );

    createBookPrinterPlugin(
      tinymce,
      book.value,
      outlines.value,
      () => {
        isLoadingChapter.value = false;
      },
      () => {
        isLoadingChapter.value = true;
      },
    );

    createExportToWordPlugin(
      tinymce,
      book.value,
      outlines.value,
      () => {
        isLoadingChapter.value = false;
      },
      () => {
        isLoadingChapter.value = true;
      },
    );
    createExportBookImagesPlugin(
      tinymce,
      book.value,
      outlines.value,
      () => {
        isLoadingChapter.value = false;
      },
      () => {
        isLoadingChapter.value = true;
      },
    );
    createFootnotePlugin(tinymce);
    createImagePlugin(tinymce, () => {
      drawerRight.value = true;
      selectedNav.value = 'images';
      // selectedPrompt.value = 'upload-image';
    });
    bookChapterSelect(
      tinymce,
      chapterOptions,
      selectedChapterId,
      selectedChapterOutlineTitle,
      () => {
        if (window.selectedChapterIdInToolbar)
          selectedChapterId.value = window.selectedChapterIdInToolbar;
      },
    );
    registerMannyButtonsPlugin(tinymce, (promptId: string) => {
      drawerRight.value = true;
      selectedNav.value = 'prompts';
      selectedPrompt.value = promptId;
      const { selection } = editor;
      const selectionText = selection.getContent();
      selectedPromptText.value = selectionText;
      window.selectedTextInEditor = selectionText;
      window.savedSelection = editor.selection.getBookmark(2);
    });
  },
};

// adds warning when maintenance mode is enabled
// saves new version of the book
watch(isMaintenanceMode, async (isWarningMaintenance) => {
  if (isWarningMaintenance === true) {
    isLoadingChapter.value = true;
    const warningDialog = $q.dialog({
      title: 'Notice: Saving New Book Version',
      message:
        '     <div class="instruct-dialog-title align-center">' +
        '        <img src="robot.png" width="42px" class="q-mr-sm" />' +
        '        You will be automatically redirected to the maintenance page once the new version of your book is saved. Thank you!' +
        '      </div>',
      progress: true,
      persistent: true,
      html: true,
      ok: false,
    });
    try {
      const formattedDateTime = getTodayFormattedDateTime();
      const versionNote = {
        Date: formattedDateTime,
        Reason: 'Manny Maintenance Update',
      };
      const description = Object.entries(versionNote)
        .map(([key, value]) => `${key}: ${value}`)
        .join('\n\n');
      await createBookVersion(
        route.params.id as string,
        formattedDateTime,
        description,
      );
      setTimeout(() => {
        isLoadingChapter.value = false;
        warningDialog.hide();
        window.location.href = '/maintenance';
      }, 10000);
    } catch (e) {
      console.error(e);
      window.location.href = '/maintenance';
    }
  }
});

// Version
async function addVersion() {
  const saveNewVersion = await new Promise((resolve) => {
    $q.dialog({
      title: 'Save New Version',
      message: 'Are you sure you want to save a new version?',
      ok: {
        color: 'primary',
      },
      cancel: {
        color: 'negative',
      },
    })
      .onOk(() => resolve(true))
      .onCancel(() => resolve(false));
  });
  if (!saveNewVersion) return;
  isLoadingChapter.value = true;
  try {
    await createBookVersion(route.params.id as string);
  } catch (e) {
    console.error(e);
    alert('Error creating version');
  } finally {
    isLoadingChapter.value = false;
  }
}
</script>

<style lang="scss">
.tox-tinymce-aux {
  z-index: 11 !important;
}
.book-editor {
  .q-toolbar {
    min-height: 0px;
  }
  .q-tabs.fixed-bottom-left {
    box-shadow:
      0 1rem 1rem -0.625rem rgba(34, 47, 62, 0.15),
      0 0 2.5rem 1px rgba(34, 47, 62, 0.15);
    border-radius: 10px;
    border: 1px solid rgba(0, 0, 0, 0.12);
    background: #fff;
  }
  .q-splitter__separator-area > * {
    top: 25px !important;
  }

  #editor-container {
    box-shadow:
      0 1rem 1rem -0.625rem rgba(34, 47, 62, 0.15),
      0 0 2.5rem 1px rgba(34, 47, 62, 0.15);
    border-radius: 10px;
  }

  #editor-container .tox .tox-editor-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    box-shadow:
      0 2px 2px -2px rgba(34, 47, 62, 0.1),
      0 8px 8px -4px rgba(34, 47, 62, 0.07);
    padding: 4px 0;
  }

  #editor {
    border-top: 0;
    height: calc(100vh - 108px) !important;
    overflow: auto;
    margin: 0;
    padding: 24px;
    scroll-behavior: smooth;
    font-family: 'Times New Roman';
    font-size: 16px;
    background: none !important;
    border: none !important;
  }

  #toolbar {
    // height: 40px;
    // background: white;
    // border: 1px solid rgba(0, 0, 0, 0.12) !important;
  }

  .tox-statusbar__wordcount {
    margin-left: 0 !important;
  }

  .tox-statusbar__branding {
    display: none !important;
  }

  #editor-container {
    transition: opacity 0.5s;
  }

  #actions-tooltip {
    position: absolute;
  }

  #editor-container .tox-tinymce {
    display: flex !important;
    visibility: visible !important;
  }

  .tox-tinymce .tox-editor-header {
    border: 0 !important;
  }

  .tox-fullscreen .tox.tox-tinymce.tox-fullscreen {
    z-index: 9999 !important;
  }

  #meta span {
    color: $primary;
    font-weight: 500;
  }

  .tox-tinymce-aux {
    display: flex !important;
  }

  .image-ref {
    color: $primary !important;
  }

  .tox-dialog__footer-end .tox-button[title='Find'],
  .tox-dialog__footer-end .tox-button[title='Save'] {
    background-color: $primary !important;
    border-color: $primary !important;
  }

  .compose-actions {
    width: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    background: lightgray;
    padding: 8px;
    border-radius: 5px;
  }

  .exit-btn {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 25px;
    height: 25px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 100%;
    background: red;
    cursor: pointer;
    z-index: 999;
  }

  .actions-row {
    display: flex;
    justify-content: flex-end;
  }

  .action-btn {
    cursor: pointer;
  }

  .reply-header {
    background: white;
    padding: 4px;
    border-radius: 4px;
    margin: 8px 0;
  }

  .reply-content {
    background: white;
    padding: 4px;
    border-radius: 4px;
    margin: 8px 0;
    font-style: italic;
  }
}
</style>
