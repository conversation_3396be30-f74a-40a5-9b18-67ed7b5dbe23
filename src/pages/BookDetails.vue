<template>
  <q-drawer
    v-model="drawerRight"
    side="right"
    bordered
    :width="
      $q.screen.lt.sm ? browserWidth + minusShowToggleButton : sideNavWidth
    "
    :behavior="$q.screen.lt.sm || $q.screen.lt.md ? 'mobile' : 'desktop'"
  >
    <RightDrawer
      v-if="!isInVersionPreview"
      v-model:selectedNav="selectedNav"
      v-model:drawerRight="drawerRight"
      v-model:splitterModel="splitterModel"
      v-model:isMannyLoading="isMannyLoading"
      v-model:selectedPrompt="selectedPrompt"
      v-model:slideReview="slideReview"
      v-model:transcripts="transcripts"
      v-model:canTranscribe="canTranscribe"
      :selectedPromptText="selectedPromptText"
      v-model:outlines="outlines"
      v-model:selectedChapterOutline="selectedChapterOutline"
      :is-mobile-view="isSmallerScreen"
      :chapter-max-width="chapterMaxWidth"
      :is-in-version-preview="isInVersionPreview"
      v-model:editing-dialog-active="editingDialogActive"
      v-model:book="book"
      v-model:is-loading-chapter="isLoadingChapter"
      :selected-chapter-id="selectedChapterId"
      :editorContent="editorContent"
    />
  </q-drawer>
  <q-splitter
    class="book-editor"
    v-model="splitterModel"
    disable
    unit="px"
    :limits="[10, Infinity]"
    separator-style="z-index: 1"
  >
    <template v-slot:separator>
      <q-btn
        round
        color="primary"
        size="xs"
        :icon="splitterModel > 10 ? 'chevron_left' : 'chevron_right'"
        @click="toggleSplitter"
      />
    </template>

    <template v-slot:before>
      <div id="left-panel">
        <q-toolbar class="q-pa-md flex justify-between">
          <q-input
            v-model="book.title"
            class="q-pa-none text-h5"
            :borderless="titleBorderless"
            @focusin="titleBorderless = false"
            @mouseenter="titleBorderless = false"
            @mouseleave="titleBorderless = true"
            input-style="font-weight: bold; padding: 0; "
            dense
          />
          <q-btn
            icon="edit_note"
            dense
            flat
            round
            v-if="!isInVersionPreview && !isReviewMode"
            @click="
              drawerRight = true;
              selectedNav = 'details';
            "
          >
            <q-tooltip>Edit Book Details</q-tooltip>
          </q-btn>
        </q-toolbar>
        <Chapters
          class="q-pa-md"
          :isInVersionPreview="isInVersionPreview"
          v-model:book="book"
          v-model:outlines="outlines"
          v-model:selectedChapterId="selectedChapterId"
          v-model:isLoadingChapter="isLoadingChapter"
          v-model:chapterMaxWidth="chapterMaxWidth"
          @toggleDrawer="toggleOutline"
        />
      </div>
    </template>

    <template v-slot:after>
      <div class="flex items-start justify-center q-pa-md">
        <div
          id="write"
          class="container"
          style="width: 100%"
          v-if="selectedChapterId != null"
        >
          <div id="editor-container">
            <div id="toolbar"></div>
            <editor
              id="editor"
              :style="
                $q.screen.lt.sm
                  ? 'height:  calc(100vh - 170px) !important;'
                  : 'height:  calc(100vh - 118px) !important;'
              "
              v-model="editorContent"
              api-key="yk2zcr92ljrjqpxrxbfo8p5914oyhcka9eowu2gohwtzuoro"
              :disabled="isInVersionPreview"
              :init="editorInit"
            />
          </div>
        </div>

        <q-inner-loading
          v-if="!isSmallerScreen"
          :showing="isLoadingChapter"
          color="primary"
          label="Hang tight, Saving...."
          label-class="text-primary"
          label-style="font-size: 1.1em"
          style="z-index: 1"
        >
          <q-spinner-dots color="primary" size="2em" />
          <div class="align-center">
            <q-icon name="img:robot.png" class="q-ml-xs" size="xl" />
            Hang tight, Loading....
          </div>
        </q-inner-loading>
        <q-inner-loading
          v-else
          :showing="isLoadingChapter && splitterModel === 10"
          color="primary"
          label="Hang tight, Saving...."
          label-class="text-primary"
          label-style="font-size: 1.1em"
          style="z-index: 1"
        >
          <q-spinner-dots color="primary" size="2em" />
          <div class="align-center">
            <q-icon name="img:robot.png" class="q-ml-xs" size="xl" />
            Hang tight, Loading....
          </div>
        </q-inner-loading>
      </div>
    </template>
  </q-splitter>
  <q-dialog v-model="editingDialogActive" persistent>
    <q-card
      :style="{
        minWidth: isSmallerScreen ? browserWidth + 'px' : 'min(70vw, 1024px)',
        maxWidth: isSmallerScreen ? browserWidth + 'px' : '768px',
      }"
      style="width: 100%; min-height: 90%"
    >
      <NewAutoBioFlow
        :initial-values="book"
        :initial-outlines="outlines.outlines"
        @updated="(id, book) => updateCurrentBook(book)"
        @cancel="editingDialogActive = false"
        v-if="book.category == 'autobiography'"
        shouldUpdate
      />
      <NewFaithStoryFlow
        :initial-values="book"
        :initial-outlines="outlines.outlines"
        @updated="(id, book) => updateCurrentBook(book)"
        @cancel="editingDialogActive = false"
        v-if="book.category == 'faithstory'"
        shouldUpdate
      />
      <NewBookDialog
        :initial-values="book"
        :initial-outlines="outlines.outlines"
        @updated="(id, book) => updateCurrentBook(book)"
        @cancel="editingDialogActive = false"
        v-else
        shouldUpdate
      />
    </q-card>
  </q-dialog>

  <q-dialog v-model="setSelectVoiceDialogActive" persistent>
    <q-card class="row no-wrap">
      <AudioBookSelectVoice
        @cancelAudioBook="setSelectVoiceDialogActive = false"
        v-model:book="book"
        v-model:outlines="outlines"
      />
    </q-card>
  </q-dialog>

  <Teleport to="#book-actions" v-if="!isSmallerScreen && !isReviewMode">
    <q-btn
      :class="isTopNavDefault ? '' : 'q-ml-xs'"
      :dense="isTopNavDefault"
      :stretch="isTopNavDefault"
      :color="!isTopNavDefault ? 'primary' : ''"
      :flat="isTopNavDefault"
      :rounded="!isTopNavDefault"
      :padding="!isTopNavDefault ? '13px' : ''"
      @click="editingDialogActive = true"
      :disable="isInVersionPreview"
      :icon="!isTopNavDefault ? 'auto_stories' : ''"
    >
      &nbsp;&nbspBook Foundations
    </q-btn>
  </Teleport>
  <Teleport to="#review-status">
    <q-btn
      :class="isTopNavDefault ? '' : 'q-mr-xs'"
      :dense="isTopNavDefault"
      :stretch="isTopNavDefault"
      :color="!isTopNavDefault ? 'primary' : 'white'"
      :flat="isTopNavDefault"
      :rounded="!isTopNavDefault"
      :padding="!isTopNavDefault ? '13px' : ''"
      v-if="isInVersionPreview"
      :to="`/books/${route.params.id}/versions`"
      icon="close"
    >
      &nbsp;Exit
      <q-tooltip>EXIT PREVIEW VERSION</q-tooltip>
    </q-btn>
  </Teleport>
  <Teleport to="#right-drawer" v-if="!isInVersionPreview">
    <q-btn
      v-if="selectedChapterOutline?.wordCount > 250"
      icon="img:robot.png "
      :class="isTopNavDefault ? '' : 'q-mr-xs'"
      :dense="isTopNavDefault"
      :stretch="isTopNavDefault"
      :color="!isTopNavDefault ? 'primary' : 'white'"
      :flat="isTopNavDefault"
      :round="!isTopNavDefault"
      :padding="!isTopNavDefault ? '13px' : ''"
      @click="
        toggleChatPage = true;
        hasChangesOnThread = selectedChapterOutline?.assistantbot?.isUpdate;
      "
    >
      <q-badge floating rounded color="orange" align="bottom">
        <q-spinner-dots size="0.7rem"
      /></q-badge>
      <q-tooltip>Chat With Manny</q-tooltip>
      <q-badge
        color="yellow"
        align="bottom"
        rounded
        floating
        style="bottom: 0; top: initial"
      />
    </q-btn>
    <q-btn
      :icon="
        !drawerRight
          ? 'keyboard_double_arrow_left'
          : 'keyboard_double_arrow_right'
      "
      :class="isTopNavDefault ? '' : 'q-mr-xs'"
      :dense="isTopNavDefault"
      :stretch="isTopNavDefault"
      :color="!isTopNavDefault ? 'primary' : 'white'"
      :flat="isTopNavDefault"
      :round="!isTopNavDefault"
      :padding="!isTopNavDefault ? '13px' : ''"
      @click="toggleDrawer"
    >
      <q-tooltip>Toggle right drawer</q-tooltip>
    </q-btn>
  </Teleport>
  <Teleport to="#header-title" v-if="isTopNavDefault">
    {{
      browserWidth <= 540
        ? trimText(book.title || 'Untitled book', 15)
        : book.title || 'Untitled book'
    }}
    <q-tooltip>{{ book.title }}</q-tooltip>
  </Teleport>
  <ChatPage
    v-if="
      !isInVersionPreview &&
      editorContent &&
      selectedChapterOutline?.wordCount > 250
    "
    :book="book"
    v-model:hasChangesOnThread="hasChangesOnThread"
    v-model:toggleChat="toggleChatPage"
    v-model:selectedChapterOutline="selectedChapterOutline"
    :isLoadingChapter="isLoadingChapter"
    :editorContent="editorContent"
  />

  <q-dialog v-model="transcriptionDialog">
    <BookTranscriptionApp @completed="completedTranscript" @close="" />
  </q-dialog>
</template>

<script lang="ts" setup>
import {
  ref,
  computed,
  watch,
  onMounted,
  toValue,
  type Ref,
  unref,
  onUnmounted,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useDebounceFn, watchIgnorable } from '@vueuse/core';
import type TinyMCE from 'tinymce';
import type { EditorOptions } from 'tinymce';
import Editor from '@tinymce/tinymce-vue';
import NewBookDialog from 'src/entities/book/ui/components/onboarding/nonfiction/NewBookFlow.vue';
import {
  NewAutoBioFlow,
  NewFaithStoryFlow,
} from 'src/entities/book/ui/components/onboarding';
import { confirmOverrideText, warn } from 'src/shared/lib/quasar-dialogs';
import {
  registerSpeechToTextPlugin,
  createFootnotePlugin,
  registerMannyButtonsPlugin,
  createImagePlugin,
  createBookWordCountPlugin,
  generateChapterInitialContent,
  createSaveVersionPlugin,
  createExportToWordPlugin,
  bookChapterSelect,
  mannyIconSVG,
  createMissionStatementPlugin,
  createExportBookImagesPlugin,
  createPageSetupPlugin,
  createBookPrinterPlugin,
  registerUploadChapterContentPlugin,
  createAudioBookPlugin,
  transcribeChapterMedia,
} from 'src/features/tinymce-plugins';
import {
  getBook,
  getChapter,
  type Book,
  type Chapter,
  listOutlines,
  updateBook,
  BookOutlines,
  setOutlineWordCount,
  RightDrawer,
  Chapters,
  ChapterOutline,
  getChapterTranscriptions,
} from 'src/entities/book';
import { useQuasar } from 'quasar';
import {
  BookVersion,
  createBookVersion,
  getBookVersion,
  getVersionChapter,
  listVersionOutlines,
} from 'src/features/book-versioning';
import { getTodayFormattedDateTime } from 'src/features/book-prompts';
import {
  allowUploadChapterContent,
  canPrintBook,
  isMaintenanceMode,
  isTopNavDefault,
  maxChapterMediaTranscripts,
  trimText,
} from 'src/entities/setting';
import { AudioBookSelectVoice } from 'src/entities/audiobook';
import {
  isAdmin,
  isAuthor,
  isReviewMode,
  loginAs,
  user,
} from 'src/entities/user';
import { createReviewPlugin } from 'src/features/tinymce-plugins/review';
import shortid from 'shortid';
import { ToolbarManager } from 'src/features/tinymce-toolbar';
import { logAmplitudeEvent } from 'src/entities/log';
import { ChatPage } from 'src/entities/chapterchat';
import { computedAsync } from '@vueuse/core/index';
import BookTranscriptionApp from '../entities/book/ui/components/BookTranscriptionApp.vue';
import { bookSpeech } from 'src/features/tinymce-plugins/book-speech';

declare const tinymce: typeof TinyMCE;

const route = useRoute();
const router = useRouter();

const $q = useQuasar();

// book detail parameters
const bookId = route.params.id as string;
const versionId = route.query.version as string | null;
const selectedNav = ref(isReviewMode.value ? 'reviews' : 'prompts');
const selectedPrompt = ref('');
const selectedPromptText = ref('');
const isMannyLoading = ref(false);
const toggleChatPage = ref(false);

const isInVersionPreview = computed(() => versionId != null);

const selectedChapterId = ref('introduction');
const transcriptionDialog = ref(false);

// chapter is loading
const hasChangesOnThread = ref(false);
const isLoadingChapter = ref(true);
const editorContent = ref('');
const pageSetupSetting = computed(() => book.value?.pageSetup);
const editingDialogActive = ref(false);
const setSelectVoiceDialogActive = ref(false);

// drawers and mobile responsive view
const drawerRight = ref(false);
const sideNavWidth = computed(() => ($q.screen.gt.md ? 365 : 300));
const sideNavChaptersWidth = computed(() => ($q.screen.gt.md ? 330 : 268));
const maxSideNavWidth = ref($q.screen.sizes.sm);
const remainSideWidth = 10;
const minusPaddingSideNavWidth = 32;
const minusShowToggleButton = 25;
const browserWidth = ref(window.innerWidth - minusShowToggleButton);
const isSmallerScreen = computed(
  () => browserWidth.value <= maxSideNavWidth.value,
);
const canPageSetup = computed(() => $q.screen.gt.lg);
const splitterModel = ref(
  isSmallerScreen.value ? remainSideWidth : sideNavWidth.value,
);
const chapterMaxWidth = ref(
  isSmallerScreen.value
    ? browserWidth.value - minusPaddingSideNavWidth
    : sideNavChaptersWidth.value,
);

// misc
const titleBorderless = ref(true);

// get the book and outline
/**
 * Fetches book data based on bookId and versionId.
 * @param {string} bookId - The ID of the book.
 * @param {string | null} versionId - The version ID of the book.
 * @returns {Promise<Book>} - The book data.
 */
const getBookData = async (
  bookId: string,
  versionId: string | null,
): Promise<Ref<BookVersion | Book>> => {
  const bookData = versionId
    ? await getBookVersion(bookId, versionId)
    : await getBook(bookId);
  selectedNav.value =
    user.value.uid != bookData.value.authorId ? 'reviews' : 'prompts';

  return bookData;
};
const book: Ref<Book> = ref(await getBookData(bookId, versionId));

/**
 * Fetches outlines data based on bookId and versionId.
 * @param {string} bookId - The ID of the book.
 * @param {string | null} versionId - The version ID of the book.
 * @returns {Promise<BookOutlines>} - The outlines data.
 */
const getOutlinesData = async (
  bookId: string,
  versionId: string | null,
): Promise<BookOutlines> => {
  const outlinesData = versionId
    ? await listVersionOutlines(bookId, versionId)
    : await listOutlines(bookId);

  if (outlinesData.value.outlines) {
    outlinesData.value.outlines = outlinesData.value?.outlines.filter(
      (outline) => !outline.isSection,
    );
  }

  return outlinesData.value;
};
const outlines: Ref<BookOutlines> = ref(
  await getOutlinesData(bookId, versionId),
);

/**
 * Fetches chapter data based on bookId, versionId, and chapterId.
 * @param {string} bookId - The ID of the book.
 * @param {string | null} versionId - The version ID of the book.
 * @returns {Promise<Chapter>} - The chapter data.
 */
const getChapterData = async (bookId: string, versionId: string | null) => {
  return versionId
    ? await getVersionChapter(bookId, versionId, selectedChapterId.value)
    : await getChapter(bookId, selectedChapterId.value);
};
const selectedChapter: Ref<Chapter> = ref(
  await getChapterData(bookId, versionId),
);

// Transcriptions
const canTranscribe = ref(false);
const transcripts = computedAsync(async () => {
  // isLoadingChapter.value = true;
  const data = await getChapterTranscriptions(
    book.value.id,
    selectedChapterId.value,
  );

  canTranscribe.value =
    data.length < maxChapterMediaTranscripts.value && !isInVersionPreview.value;
  // isLoadingChapter.value = false;
  return data;
}, []);

/**
 * Toggles the splitter's position based on the current screen size and conditions.
 * Adjusts the `splitterModel.value` to either the minimum allowed width or the appropriate width
 * based on screen size and saves this value to `book.value.drawer.left`.
 */
const toggleSplitter = (): void => {
  if (splitterModel.value > remainSideWidth)
    splitterModel.value = remainSideWidth;
  else
    splitterModel.value = isSmallerScreen.value
      ? browserWidth.value
      : sideNavWidth.value;

  book.value.drawer.left = splitterModel.value;
};
const toggleOutline = async (): void => {
  isMannyLoading.value = false;
  await new Promise((resolve) => setTimeout(resolve, 200));
  selectedNav.value = 'outline';
  drawerRight.value = true;
  isMannyLoading.value = true;
};

const totalWordCount = computed(() => {
  return (
    outlines.value.introduction.wordCount +
    outlines.value.conclusion.wordCount +
    outlines.value.outlines.reduce((acc, ch) => ch.wordCount + acc, 0)
  );
});

const selectedChapterOutlineTitle = computed(() => {
  return getSelectedChapterOutlineTitle();
});

const chapterOptions = computed(() => [
  {
    label: 'Introduction',
    value: 'introduction',
  },
  ...outlines.value.outlines?.map((chapter, idx) => ({
    label:
      'Chapter ' + (idx + 1) + (chapter.title ? ` | ${chapter.title}` : ''),
    value: chapter.id,
  })),
  {
    label: 'Conclusion',
    value: 'conclusion',
  },
]);

/**
 * Returns the selected chapter outline based on the chapter ID and outlines.
 * @returns {ChapterOutline | undefined} - The selected chapter outline.
 */
const getSelectedChapterOutline = (): ChapterOutline => {
  const chapterId = selectedChapterId.value;
  if (chapterId === 'introduction') {
    return outlines.value.introduction as ChapterOutline;
  }
  if (chapterId === 'conclusion') {
    return outlines.value.conclusion as ChapterOutline;
  }
  return outlines.value.outlines.find((outline) => {
    if (outline && outline.id) {
      return outline.id === chapterId;
    } else {
      return true;
    }
  }) as ChapterOutline;
};

/**
 * Sets the selected chapter outline based on the chapter ID and updates the outlines.
 * @param {ChapterOutline} value - The new chapter outline value.
 */
const setSelectedChapterOutline = (value: any): void => {
  if (!outlines) {
    return;
  }
  const chapterId = selectedChapterId.value;
  if (chapterId === 'introduction') {
    outlines.value.introduction = value;
  } else if (chapterId === 'conclusion') {
    outlines.value.conclusion = value;
  } else {
    const outlineIdx = outlines.value.outlines.findIndex(
      (outline) => outline.id === chapterId,
    );
    if (outlineIdx != -1 && outlines.value.outlines) {
      outlines.value.outlines[outlineIdx] = value;
    }
  }
};
const selectedChapterOutline = computed({
  get() {
    return getSelectedChapterOutline();
  },
  set(value) {
    setSelectedChapterOutline(value);
  },
});
const isUploadContentButtonDisabled = computed(
  () => selectedChapterOutline.value.wordCount === 0,
);
// resize listener
onMounted(() => {
  window.addEventListener('resize', updateWidth);
  initBook();
});

onUnmounted(() => {
  window.removeEventListener('resize', updateWidth);
});

// watch - handleChapterChange
/**
 * Handles chapter change and updates the editor content.
 * @param {Chapter} chapter - The new chapter.
 * @param {Chapter} oldChapter - The old chapter.
 */
const handleChapterChange = (chapter: Chapter, oldChapter: any): void => {
  window.selectedChapterId = selectedChapterOutline.value.id;
  window.selectedChapter =
    selectedChapterOutline.value?.title ?? selectedChapterOutline.value?.id;

  if (!chapter || chapter?.id === oldChapter?.id) {
    return;
  }

  const escapedTitle = selectedChapterOutline.value.title.replace(
    /&/g,
    '&amp;',
  );

  if (!selectedChapterOutline.value.title) {
    if (selectedChapterId.value === 'introduction') {
      selectedChapterOutline.value.title = 'Introduction';
    } else if (selectedChapterId.value === 'conclusion') {
      selectedChapterOutline.value.title = 'Conclusion';
    }
  }
  if (selectedChapterOutline.value.title) {
    const regex = new RegExp(`<h1(?:\\s+[^>]+)?>${escapedTitle}</h1>`, 'gi');
    const chapterContent = chapter.content.replace(regex, '');
    editorContent.value = `<h1 class="chapter-title">${selectedChapterOutline.value.title}</h1> ${chapterContent}`;
  } else {
    editorContent.value = chapter.content;
  }
};
watch(
  selectedChapter,
  (chapter, oldChapter) => {
    handleChapterChange(chapter, oldChapter);
  },
  { immediate: true },
);

// watch - handleEditorContentChange
const { ignoreUpdates: ignoreTitleUpdates } = watchIgnorable(
  () => selectedChapterOutline.value?.title,
  (title, oldTitle) => {
    editorContent.value =
      (title ? `<h1 class="chapter-title">${title}</h1>\n` : '') +
      selectedChapter.value?.content;
  },
);
/**
 * Handles changes in the editor content and updates the corresponding chapter outline and content.
 * @param {string} content - The new editor content.
 * @param {Function} ignoreTitleUpdates - Function to ignore title updates.
 */
const handleEditorContentChange = (content: string): void => {
  if (!selectedChapterOutline.value || !selectedChapter.value) {
    return;
  }

  const title = content.match(/<h1(?:\s+[^>]+)?>(.*?)<\/h1>/)?.[1] ?? '';

  if (selectedChapterOutline.value.title !== title) {
    ignoreTitleUpdates(() => {
      selectedChapterOutline.value.title = title;
    });
  }

  const chapterContent = title
    ? content.replace(/<h1(?:\s+[^>]+)?>.*?<\/h1>/, '')
    : content;
  selectedChapter.value.content = chapterContent;

  const bookWordCount = setOutlineWordCount(
    book.value.id,
    title,
    outlines,
    selectedChapterOutline.value.id,
    selectedChapter.value.content,
  );

  outlines.value = bookWordCount.outline;

  selectedChapterOutline.value.wordCount = bookWordCount.wordCount;
};
watch(editorContent, (content) => {
  handleEditorContentChange(content);
});

// watch - handleSelectedChapterIdChange
isLoadingChapter.value = false;
window.dictation = '';
/**
 * Handles overridding the default behavior of the undo/redo commands by creating custom commands that do nothing:
 */
const handleClearingOfUndo = () => {
  const editor = tinymce.get('editor');
  if (editor) {
    editor.undoManager.clear();
  }
};
/**
 * Handles changes to the selected chapter ID.
 * It fetches the chapter content based on the current version or directly from the book and updates the chapter state.
 */
const handleSelectedChapterIdChange = async (): Promise<void> => {
  isLoadingChapter.value = true;

  if (window.dictation) {
    selectedChapterId.value = window.dictation;
    $q.notify({
      badgeStyle: 'opacity: 0',
      message:
        'Please remember to disable dictation before moving on to the next chapter.',
    });
  }

  selectedChapter.value = versionId
    ? await getVersionChapter(bookId, versionId, selectedChapterId.value)
    : toValue(await getChapter(bookId, selectedChapterId.value));

  await new Promise((resolve) => setTimeout(resolve, 400));
  handleClearingOfUndo();
  isLoadingChapter.value = false;
};
watch(selectedChapterId, async () => {
  await handleSelectedChapterIdChange();
});

// watch - handleMaintenanceMode
/**
 * Handles maintenance mode by saving data before switching to maintenance mode.
 * @param {boolean} isWarningMaintenance - Check if maintenance mode is on
 */
const handleMaintenanceMode = async (isWarningMaintenance): void => {
  if (isWarningMaintenance === true) {
    isLoadingChapter.value = true;
    const warningDialog = $q.dialog({
      title: 'Notice: Saving New Book Version',
      message:
        '     <div class="instruct-dialog-title align-center">' +
        '        <img src="robot.png" width="42px" class="q-mr-sm" />' +
        '        You will be automatically redirected to the maintenance page once the new version of your book is saved. Thank you!' +
        '      </div>',
      progress: true,
      persistent: true,
      html: true,
      ok: false,
    });
    try {
      const formattedDateTime = getTodayFormattedDateTime();
      const versionNote = {
        Date: formattedDateTime,
        Reason: 'Manny Maintenance Update',
      };
      const description = Object.entries(versionNote)
        .map(([key, value]) => `${key}: ${value}`)
        .join('\n\n');
      await createBookVersion(
        route.params.id as string,
        formattedDateTime,
        description,
      );
      setTimeout(() => {
        isLoadingChapter.value = false;
        warningDialog.hide();
        window.location.href = '/maintenance';
      }, 10000);
    } catch (e) {
      console.error(e);
      window.location.href = '/maintenance';
    }
  }
};
watch(isMaintenanceMode, async (isWarningMaintenance) => {
  if (isWarningMaintenance && !isAdmin.value) {
    await handleMaintenanceMode(isWarningMaintenance);
  }
});

// other methods
/**
 * Updates the width of the browser window and adjusts related properties.
 */
const updateWidth = (): void => {
  const resizedWidth = window.innerWidth - minusShowToggleButton;
  // do not do anything if same width
  if (resizedWidth === browserWidth.value) return;
  browserWidth.value = window.innerWidth - minusShowToggleButton;
  splitterModel.value = isSmallerScreen.value
    ? remainSideWidth
    : sideNavWidth.value;
  chapterMaxWidth.value = isSmallerScreen.value
    ? browserWidth.value - minusPaddingSideNavWidth
    : sideNavChaptersWidth.value;
};

/**
 * Initializes the book with default page setup and drawer properties.
 */
const initBook = async (): void => {
  if (book.value.authorId !== user?.value?.uid) {
    if (!isReviewMode.value && !loginAs.value && !isAdmin.value)
      await router.push({ path: '/' });
  }
  if (!isAdmin.value && !isAuthor.value) await router.push({ path: '/' });

  if (!book.value?.pageSetup) {
    book.value.pageSetup = {
      selectbox: 'default',
      marginleft: 1,
      marginright: 1,
      margintop: 1,
      marginbottom: 1,
      style: '',
    };
  }

  if (!book.value?.drawer) {
    book.value.drawer = {
      left: splitterModel.value,
      right: false,
    };
  }

  if (canPageSetup.value && !isInVersionPreview) {
    splitterModel.value = book.value.drawer.left;
    drawerRight.value = book.value.drawer.right;
  }
};

/**
 * Toggles the state of the drawer.
 */
const toggleDrawer = (): void => {
  book.value.drawer.right = !drawerRight.value;
  drawerRight.value = !drawerRight.value;
};

/**
 * Returns the title of the selected chapter outline based on the chapter ID and outlines.
 * @returns {string} - The title of the selected chapter outline.
 */
const getSelectedChapterOutlineTitle = (): string => {
  const chapterId = selectedChapterId.value;
  const isSmallerScreenValue = isSmallerScreen.value;
  if (!outlines.value) {
    return '';
  }
  if (chapterId === 'introduction') {
    return 'Introduction';
  }
  if (chapterId === 'conclusion') {
    return 'Conclusion';
  }

  if (!outlines.value.outlines) return '';

  const chapterNumber = outlines.value.outlines.findIndex(
    (outline) => outline.id === chapterId,
  );
  if (chapterNumber != -1) {
    const outline = outlines.value.outlines[chapterNumber];
    return outline && !isSmallerScreenValue
      ? `Chapter ${chapterNumber + 1} ${
          outline.title ? `| ${outline.title}` : ''
        }`
      : `Chapter ${chapterNumber + 1}`;
  } else {
    return '';
  }
};

/**
 * Updates the current book with new data and handles the state after the update.
 * @param {Book} newData - The new data for the book.
 */
const updateCurrentBook = async (newData: Book): Promise<void> => {
  if (!newData.id) {
    newData.id = bookId;
  }
  await updateBook(newData.id, newData);
  book.value = newData;
  editingDialogActive.value = false;
  $q.notify('Book updated successfully!');
  outlines.value = unref(await listOutlines(newData.id));
  // Filter out outlines with isSection === true
  if (outlines.value.outlines) {
    outlines.value.outlines = outlines.value.outlines.filter(
      (outline) => !outline.isSection,
    );
  }
};

// Review Feature
const slideReview = ref(0);
if (isReviewMode.value && book.value.authorId !== isReviewMode.value)
  isReviewMode.value = '';

// Toolbar
const toolbarManager = new ToolbarManager(
  allowUploadChapterContent.value,
  canPageSetup.value,
  canPrintBook.value,
  isReviewMode.value,
);
const mannyFileButtons = toolbarManager.getMannyFileButtons();
const toolbarButtons = toolbarManager.getToolbarButtons($q.screen.lt.lg);
const menuBars = toolbarManager.getMenuBars();
const moreButtons = toolbarManager.getMoreButtons();
const selectionMenus = $q.screen.lt.sm
  ? ''
  : toolbarManager.getSelectionButtons();
const mannyButtons = toolbarManager.getMenuMannyButtons();

/**
 * Adds a new chapter review.
 */
const addNewReview = (bookmark) => {
  if (selectedPromptText.value) {
    const id = shortid.generate();
    const newComment = {
      id: id,
      createdAt: Date.now(),
      resolvedAt: '',
      isResolved: false,
      chapterId: selectedChapterId.value,
      content: '',
      selection: selectedPromptText.value,
      selectedBookmark: bookmark,
      replies: [],
      userId: user.value?.uid ?? '',
      readBy: [],
      isTypings: {},
      resolveSelection: '',
      revhistory: [],
      user: {
        name: user.value?.displayName,
        email: user.value?.email,
      },
    };
    book.value.comments?.push(newComment);
    slideReview.value = shortid.generate();
    // setBook(book.value.id, { comments: book.value.comments });
  }
};

const completedTranscript = (data: any) => {
  const editor = tinymce.get('editor');
  if (editor) {
    const evId = 'insert-' + Date.now();
    const contentResponse = `<div id="${evId}">${data.content}</div>`;
    const currentBookmark = editor.selection.getBookmark(2);
    if (currentBookmark) {
      editor.selection.moveToBookmark(currentBookmark);
      editor.selection.collapse();
      editor.insertContent(contentResponse, { format: 'html' });
    } else {
      editor.selection.setContent(contentResponse, { format: 'html' });
    }
  }
  transcriptionDialog.value = false;
};

const editorInit: Partial<EditorOptions> = {
  height: $q.screen.lt.sm ? 'calc(100vh - 200px)' : 'calc(100vh - 116px)',
  menubar: menuBars,
  menu: {
    file: {
      title: 'File',
      items: mannyFileButtons,
    },
    manuscriptr: {
      title: 'Manny',
      items: mannyButtons,
    },
    edit: {
      title: 'Edit',
      items:
        'undo redo | cut copy paste pastetext | selectall | searchreplace | ',
    },
    insert: {
      title: 'Insert',
      items:
        'image link media template codesample inserttable | charmap hr pagebreak nonbreaking anchor toc | insertdatetime',
    },
    format: {
      title: 'Format',
      items:
        'bold italic underline strikethrough superscript subscript codeformat | blockquote | forecolor backcolor | removeformat',
    },
    tools: {
      title: 'Tools',
      items: 'wordcount',
    },
  },
  // skin: 'tinymce-5',
  plugins: [
    'book-audio-book',
    'book-upload-chapter-content',
    'book-printer',
    'book-page-setup',
    'export-book-images',
    'mission-statement',
    'book-chapter-select',
    'generate-chapter-content',
    'export-to-word',
    'book-word-count',
    'transcribe-chapter-media',
    'save-version',
    'wordcount',
    'advlist',
    'autolink',
    'lists',
    'link',
    'searchreplace',
    'nonbreaking',
    'quickbars',
    'custom-comment',
    'custom-footnote',
    'custom-image',
    'manny-buttons',
    'book-speech',
    'table',
  ],
  toolbar_mode: $q.screen.lt.lg ? 'scrolling' : 'floating',
  toolbar: toolbarButtons,
  formats: {
    underline: { inline: 'u' },
  },
  block_formats:
    'Paragraph=p; Chapter Title=h1; Chapter Section=h2; Chapter Subsection=h3',
  fontsize_formats:
    '12=12px 14=14px 16=16px 18=18px 24=24px 32=32px 36=36px 48=48px',
  advlist_bullet_styles: 'disc',
  advlist_number_styles:
    'default,upper-alpha,upper-roman,lower-alpha,lower-roman',
  nonbreaking_force_tab: true,
  nonbreaking_wrap: true,
  statusbar: true,
  paste_as_text: true,
  paste_block_drop: true,
  quickbars_selection_toolbar: selectionMenus,
  quickbars_insert_toolbar: '',
  contextmenu: `undo redo | blocks | selectall | ${moreButtons}`,
  inline: false,
  fixed_toolbar_container: '#toolbar',
  elementpath: false,
  content_css: '/tinymceStyles.css',
  init_instance_callback: (editor) => {
    editor.focus();
    isLoadingChapter.value = false;
  },
  setup: (editor) => {
    // Event listener for pasting
    editor.on('paste', function (e) {
      // Access and modify the pasted content here if needed
      const htmlContent = e.clipboardData.getData('text/html');
      if (htmlContent) {
        editor.insertContent(htmlContent); // Insert the HTML as rendered content
        e.preventDefault(); // Prevent default pasting behavior
      }
    });
    // Event listener for selection changes
    editor.on('NodeChange', (e) => {
      const { selection } = editor;
      const selectionText = selection.getContent();
      selectedPromptText.value = selectionText;
    });
    editor.on('change', (e) => {
      if (!selectedChapterOutline.value?.assistantbot) {
        selectedChapterOutline.value.assistantbot = {
          isUpdate: true,
        };
      }
      selectedChapterOutline.value.assistantbot.isUpdate = true;
    });

    isLoadingChapter.value = true;
    // group more icons
    editor.ui.registry.addGroupToolbarButton('more', {
      icon: 'more-drawer',
      tooltip: 'More Options',
      items: moreButtons,
    });
    editor.ui.registry.addIcon('mannyicon', mannyIconSVG);
    editor.ui.registry.addGroupToolbarButton('manny', {
      icon: 'mannyicon',
      tooltip:
        "Click Manny to get started—he's here to help you write your chapter!",
      items: 'generatechaptercontent',
    });

    //require("src/beyondgrammar.js");
    // tinymce.PluginManager.add('custom-comment', () =>
    //   this.createCommentPlugin(editor),
    // );

    bookSpeech(tinymce, () => {
      transcriptionDialog.value = true;
    });

    createPageSetupPlugin(
      tinymce,
      pageSetupSetting,
      canPageSetup,
      () => {
        //isLoadingChapter.value = false;

        if (window.bookPageSetup) book.value.pageSetup = window.bookPageSetup;
      },
      () => {
        // isLoadingChapter.value = true;
      },
    );
    createBookWordCountPlugin(
      tinymce,
      totalWordCount,
      selectedChapterOutlineTitle,
    );
    createMissionStatementPlugin(tinymce, () => {
      editingDialogActive.value = true;
    });
    generateChapterInitialContent(
      tinymce,
      book.value,
      selectedChapterId,
      selectedChapterOutlineTitle,
      async () => {
        isLoadingChapter.value = true;
        if (!book.value.title || !book.value.subtitle) {
          warn($q, {
            message: 'Book title and subtitle are required',
          });
          isLoadingChapter.value = false;
          return false;
        } else if (
          !['introduction', 'conclusion'].includes(selectedChapterId.value) &&
          !selectedChapterOutline.value.title
        ) {
          warn($q, {
            message: 'A chapter name is required first',
          });
          isLoadingChapter.value = false;
          return false;
        } else {
          if (selectedChapter.value.content) {
            const shouldOverride = await confirmOverrideText($q, {
              message:
                'This action will override any chapter content you already have.',
            });

            if (!shouldOverride) {
              isLoadingChapter.value = false;
              return false;
            }
          }
          // disable selection of outline nav
          // selectedNav.value = 'outline';
          // drawerRight.value = true;
          // isGeneratingOutline.value = false;
          return true;
        }
      },
      () => {
        isLoadingChapter.value = false;
        const response = editor.getContent();
        if (response) {
          // adds chapter title to Intro and Conclusion
          if (!selectedChapterOutline.value.title) {
            if (selectedChapterId.value === 'introduction') {
              selectedChapterOutline.value.title = 'Introduction';
            } else if (selectedChapterId.value === 'conclusion') {
              selectedChapterOutline.value.title = 'Conclusion';
            }
          }
          selectedChapter.value.content = response;
          if (selectedChapterOutline.value.title) {
            const escapedTitle = selectedChapterOutline.value.title.replace(
              /&/g,
              '&amp;',
            );
            // Create a regular expression to match all <h1> elements and  Remove all existing <h1> elements with the specified title from the chapter content
            const regex = new RegExp(
              `<h1(?:\\s+[^>]+)?>${escapedTitle}</h1>`,
              'gi',
            );
            const chapterContent = response.replace(regex, '');
            selectedChapter.value.content = `<h1 class="chapter-title">${selectedChapterOutline.value.title}</h1> ${chapterContent}`;
          }
          editorContent.value = selectedChapter.value.content;
        }
      },
    );
    createSaveVersionPlugin(
      tinymce,
      bookId,
      () => {
        isLoadingChapter.value = false;
      },
      () => {
        isLoadingChapter.value = true;
      },
    );

    createBookPrinterPlugin(
      tinymce,
      book.value,
      outlines.value,
      () => {
        isLoadingChapter.value = false;
      },
      () => {
        isLoadingChapter.value = true;
      },
    );
    transcribeChapterMedia(
      tinymce,
      book.value,
      selectedChapter,
      canTranscribe,
      async () => {
        isMannyLoading.value = false;
        await new Promise((resolve) => setTimeout(resolve, 200));
        drawerRight.value = true;
        selectedNav.value = 'transcripts';
        isMannyLoading.value = true;
        return true;
      },
      () => {},
    );

    createExportToWordPlugin(
      tinymce,
      book.value,
      outlines.value,
      () => {
        isLoadingChapter.value = false;
      },
      () => {
        isLoadingChapter.value = true;
      },
    );
    createExportBookImagesPlugin(
      tinymce,
      book.value,
      outlines.value,
      () => {
        isLoadingChapter.value = false;
      },
      () => {
        isLoadingChapter.value = true;
      },
    );
    createFootnotePlugin(tinymce);
    createImagePlugin(tinymce, async () => {
      isMannyLoading.value = false;
      await new Promise((resolve) => setTimeout(resolve, 200));
      drawerRight.value = true;
      selectedNav.value = 'images';
      isMannyLoading.value = true;

      // selectedPrompt.value = 'upload-image';
    });
    createAudioBookPlugin(tinymce, () => {
      drawerRight.value = true;
      selectedNav.value = 'audiobook';
      setSelectVoiceDialogActive.value = true;
    });
    bookChapterSelect(
      tinymce,
      chapterOptions,
      selectedChapterId,
      selectedChapterOutlineTitle,
      () => {
        if (window.selectedChapterIdInToolbar)
          selectedChapterId.value = window.selectedChapterIdInToolbar;
      },
    );
    registerMannyButtonsPlugin(tinymce, async (promptId: string) => {
      drawerRight.value = true;
      selectedNav.value = 'prompts';
      selectedPrompt.value = promptId;
      const { selection } = editor;
      const selectionText = selection.getContent();
      selectedPromptText.value = selectionText;
      window.selectedTextInEditor = selectionText;
      window.savedSelection = editor.selection.getBookmark(2);

      // record amplitude event
      logAmplitudeEvent(
        user.value.uid,
        `${promptId} clicked in the editor`,
        'BookDetails',
      );
      // resets selectedPrompt after 200ms
      await new Promise((resolve) => setTimeout(resolve, 200));
      selectedPrompt.value = '';
    });

    // watch the showing and hiding of allowUploadChapterContent
    watch(
      allowUploadChapterContent,
      (allowUpload) => {
        // check in the book setting if the allowed to upload chapter content
        registerUploadChapterContentPlugin(
          tinymce,
          bookId,
          isUploadContentButtonDisabled,
          allowUploadChapterContent,
          () => {
            // Trigger reactivity (since this doesn't update modelValue)
            editorContent.value = editor.getContent();
          },
          async () => {
            if (selectedChapter.value.content) {
              return await confirmOverrideText($q);
            } else {
              return true;
            }
          },
        );
      },
      {
        immediate: true,
      },
    );

    createReviewPlugin(tinymce, () => {
      // Trigger reactivity (since this doesn't update modelValue)
      const { selection } = editor;
      let selectionText = selection.getContent({ format: 'text' });
      selectedPromptText.value = selectionText;
      addNewReview(selection.getBookmark(2));
      drawerRight.value = true;
      selectedNav.value = 'reviews';
    });
  },
};
</script>

<style lang="scss">
.tox-tinymce-aux {
  z-index: 11 !important;
}
.book-editor {
  .q-inner-loading {
    z-index: 13 !important;
  }
  .q-toolbar {
    min-height: 0px;
  }
  .q-tabs.fixed-bottom-left {
    box-shadow:
      0 1rem 1rem -0.625rem rgba(34, 47, 62, 0.15),
      0 0 2.5rem 1px rgba(34, 47, 62, 0.15);
    border-radius: 10px;
    border: 1px solid rgba(0, 0, 0, 0.12);
    background: #fff;
  }
  .q-splitter__separator-area > * {
    top: 25px !important;
  }

  #editor-container {
    //box-shadow:
    //  0 1rem 1rem -0.625rem rgba(34, 47, 62, 0.15),
    //  0 0 2.5rem 1px rgba(34, 47, 62, 0.15);
    border-radius: 10px;
  }

  #editor-container .tox .tox-editor-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    box-shadow:
      0 2px 2px -2px rgba(34, 47, 62, 0.1),
      0 8px 8px -4px rgba(34, 47, 62, 0.07);
    padding: 4px 0;
  }

  #editor {
    border-top: 0;
    overflow: auto;
    margin: 0;
    padding: 24px;
    scroll-behavior: smooth;
    font-family: 'Times New Roman';
    font-size: 16px;
    background: none !important;
    border: none !important;
  }

  #toolbar {
    // height: 40px;
    // background: white;
    // border: 1px solid rgba(0, 0, 0, 0.12) !important;
  }

  .tox-statusbar__wordcount {
    margin-left: 0 !important;
  }

  .tox-statusbar__branding {
    display: none !important;
  }

  #editor-container {
    transition: opacity 0.5s;
  }

  #actions-tooltip {
    position: absolute;
  }

  #editor-container .tox-tinymce {
    display: flex !important;
    visibility: visible !important;
  }

  .tox-tinymce .tox-editor-header {
    border: 0 !important;
  }

  .tox-fullscreen .tox.tox-tinymce.tox-fullscreen {
    z-index: 9999 !important;
  }

  #meta span {
    color: $primary;
    font-weight: 500;
  }

  .tox-tinymce-aux {
    display: flex !important;
  }

  .image-ref {
    color: $primary !important;
  }

  .tox-dialog__footer-end .tox-button[title='Find'],
  .tox-dialog__footer-end .tox-button[title='Save'] {
    background-color: $primary !important;
    border-color: $primary !important;
  }

  .compose-actions {
    width: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    background: lightgray;
    padding: 8px;
    border-radius: 5px;
  }

  .exit-btn {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 25px;
    height: 25px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 100%;
    background: red;
    cursor: pointer;
    z-index: 999;
  }

  .actions-row {
    display: flex;
    justify-content: flex-end;
  }

  .action-btn {
    cursor: pointer;
  }

  .reply-header {
    background: white;
    padding: 4px;
    border-radius: 4px;
    margin: 8px 0;
  }

  .reply-content {
    background: white;
    padding: 4px;
    border-radius: 4px;
    margin: 8px 0;
    font-style: italic;
  }
}

.upload-chapter-content-dialog {
  .q-card__section--vert {
    padding: 0;
  }
  .q-card__actions {
    &.justify-end {
      justify-content: flex-start;
    }
  }
}

.tox-pop {
  max-width: 490px !important;
  min-height: 120px !important;
  .tox-pop__dialog .tox-toolbar {
    flex-wrap: wrap !important;
  }
}
</style>
