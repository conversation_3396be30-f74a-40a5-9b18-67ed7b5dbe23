<template>
  <section
    id="export"
    class="q-mx-auto"
    :class="{
      'q-pa-lg': !$q.screen.lt.sm, // Full width on xs and sm
    }"
  >
    <q-card
      :class="{
        'full-width': $q.screen.lt.sm, // Full width on xs and sm
      }"
    >
      <q-card-section>
        <h2>Versions</h2>
      </q-card-section>
      <q-separator />
      <q-card-section class="q-pa-none">
        <q-scroll-area
          :thumb-style="thumbStyle"
          :bar-style="barStyle"
          style="height: calc(100vh - 250px)"
        >
          <q-list class="rounded-borders q-mb-lg">
            <!-- NOTE: refs inside an array do not get automatically unwrapped -->
            <div v-for="(version, index) in versions" :key="version.value.id">
              <q-expansion-item group="versions" hide-expand-icon>
                <template v-slot:header>
                  <q-item-section>
                    <q-item-label>{{
                      version.value.name ||
                      'Version ' + formatDate(version.value.createdAt)
                    }}</q-item-label>
                    <q-item-label caption>{{
                      formatDate(version.value.createdAt)
                    }}</q-item-label>
                  </q-item-section>

                  <q-item-section class="items-end">
                    <q-btn
                      flat
                      round
                      dense
                      color="dark"
                      icon="more_vert"
                      @click.stop
                      class="more-button"
                    >
                      <q-menu>
                        <q-list style="min-width: 100px">
                          <q-item
                            clickable
                            v-close-popup
                            :to="`/books/${bookId}?version=${version.value.id}`"
                          >
                            <q-item-section>Preview</q-item-section>
                          </q-item>
                          <q-item
                            clickable
                            v-close-popup
                            @click="setAsCurrent(version.value)"
                          >
                            <q-item-section
                              >Rollback to this version</q-item-section
                            >
                          </q-item>
                          <q-item
                            clickable
                            v-close-popup
                            @click="downloadBackup(version.value)"
                          >
                            <q-item-section>Download Backup</q-item-section>
                          </q-item>
                          <q-item
                            clickable
                            v-close-popup
                            @click="deleteVersion(version.value)"
                          >
                            <q-item-section>Delete</q-item-section>
                          </q-item>
                        </q-list>
                      </q-menu>
                    </q-btn>
                  </q-item-section>
                </template>
                <q-separator />
                <div class="q-pa-md">
                  <div class="col-12">
                    <q-input
                      v-model="version.value.name"
                      label="Name"
                      outlined
                      class="q-mb-md"
                      dense
                    />
                    <q-input
                      v-model="version.value.description"
                      type="textarea"
                      label="Notes"
                      outlined
                      dense
                    />
                  </div>
                </div>
                <q-separator />
              </q-expansion-item>
            </div>
          </q-list>
        </q-scroll-area>
        <q-inner-loading
          :showing="isLoading"
          color="primary"
          label="Hang tight, Saving...."
          label-class="text-primary"
          label-style="font-size: 1.1em"
          style="z-index: 1"
        >
          <q-spinner-dots color="primary" size="2em" />
          <div class="align-center">
            <q-icon name="img:robot.png" class="q-ml-xs" size="xl" />
            Hang tight, Loading....
          </div>
        </q-inner-loading>
      </q-card-section>
      <q-separator />
      <q-card-section>
        <q-card-actions
          :align="$q.screen.lt.sm ? 'center' : 'left'"
          class="q-gutter-sm"
        >
          <q-btn
            label="New Version"
            icon="storage"
            color="primary"
            :loading="addingVersion"
            :disable="addingVersion"
            @click="addVersion"
          />
          <q-btn
            label="Import Backup"
            icon="publish"
            color="primary"
            :disable="importingBackup"
            @click="showFilePicker"
          />
          <q-file
            v-show="false"
            ref="fileInput"
            v-model="file"
            accept=".json"
            @update:model-value="importBackup"
          />
        </q-card-actions>
      </q-card-section>
    </q-card>
  </section>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import { type QFile, date, useQuasar } from 'quasar';
import {
  confirmDeletion,
  confirmOverrideText,
  showError,
} from 'src/shared/lib/quasar-dialogs';
import { saveAs } from 'file-saver';
import {
  listBookVersions,
  createBookVersion,
  deleteBookVersion,
  restoreBookVersion,
  importBookVersion,
  exportBookVersion,
  type BookVersion,
  type Backup,
} from 'src/features/book-versioning';
import { readAsText } from 'src/shared/lib/file-reader';
import { barStyle, thumbStyle } from 'src/entities/setting';
import { computedAsync } from '@vueuse/core';

const route = useRoute();
const bookId = route.params.id as string;
const versions = await listBookVersions(bookId);
const isLoading = computed(
  () =>
    versions.value?.length === 0 ||
    addingVersion.value ||
    importingBackup.value,
);

const addingVersion = ref(false);
async function addVersion() {
  addingVersion.value = true;
  try {
    await createBookVersion(route.params.id as string);
  } catch (e) {
    console.error(e);
    alert('Error creating version');
  } finally {
    addingVersion.value = false;
  }
}

const $q = useQuasar();
async function setAsCurrent(version: BookVersion) {
  const confirmed = await confirmOverrideText($q, {
    title: 'Are you sure?',
    message:
      'This will save a backup copy of the current working version and begin using the selected version.',
  });
  if (!confirmed) return;
  addingVersion.value = true;
  try {
    await addVersion();
    await restoreBookVersion(bookId, version);
  } catch (e) {
    console.error(e);
    alert('Error restoring version');
  }
  addingVersion.value = false;
}

async function deleteVersion(version: BookVersion) {
  try {
    await confirmDeletion($q);
  } catch (error) {
    // operation cancelled
    return;
  }
  addingVersion.value = true;
  try {
    await deleteBookVersion(bookId, version.id);
  } catch (error) {
    console.error(error);
    alert('Error deleting version');
  }
  addingVersion.value = false;
}

const fileInput = ref<QFile>();
function showFilePicker() {
  fileInput.value?.pickFiles();
}

const importingBackup = ref(false);
const file = ref<File>();
async function importBackup() {
  if (!file.value) return;
  importingBackup.value = true;
  const fileContent = await readAsText(file.value!);
  file.value = undefined;

  try {
    const backup: Backup = JSON.parse(fileContent);
    await importBookVersion(bookId, backup);
  } catch (error) {
    showError($q, (error as Error).message);
  } finally {
    importingBackup.value = false;
  }
}

async function downloadBackup(version: BookVersion) {
  importingBackup.value = true;
  const backup = await exportBookVersion(bookId, version);
  const jsonString = JSON.stringify(JSON.parse(JSON.stringify(backup)));

  const blob = new Blob([jsonString], { type: 'application/json' });
  let filename = version.title + (version.name ? ' - ' + version.name : '');
  filename = filename?.replace(/[/\\?%*:|"<>]/g, '');
  saveAs(blob, filename + ' (backup).json');
  importingBackup.value = false;
}

function formatDate(timestamp: Date) {
  return date.formatDate(timestamp, 'M-D-YY h:mm A');
}
</script>

<style lang="scss"></style>
