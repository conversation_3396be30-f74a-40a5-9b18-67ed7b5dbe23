<template>
  <section
    id="export"
    class="q-mx-auto"
    :class="{
      'q-pa-lg': !$q.screen.lt.sm, // Full width on xs and sm
    }"
  >
    <q-card
      :class="{
        'full-width': $q.screen.lt.sm, // Full width on xs and sm
      }"
    >
      <q-card-section class="bg-primary text-white">
        <div class="row items-center">
          <q-icon name="public" size="2em" class="q-mr-md" />
          <div class="text-h5">Page Settings</div>
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <q-scroll-area
          :thumb-style="thumbStyle"
          :bar-style="barStyle"
          style="height: calc(100vh - 240px)"
        >
          <q-splitter v-model="splitterModel">
            <template v-slot:before>
              <q-tabs v-model="innerTab" vertical>
                <q-tab name="innertos" icon="fact_check" label="tos" />
                <q-tab name="innerprivacy" icon="shield" label="privacy" />
              </q-tabs>
            </template>

            <template v-slot:after>
              <q-tab-panels
                v-model="innerTab"
                animated
                transition-prev="slide-down"
                transition-next="slide-up"
              >
                <q-tab-panel name="innertos">
                  <q-editor
                    v-model="tosContent"
                    :disable="isLoading === true"
                    :toolbar="toolbarItems"
                    min-height="15em"
                    paragraph-tag="p"
                    height="calc(100vh - 340px)"
                    @update:model-value="savePage('tos', tosContent)"
                  />
                </q-tab-panel>

                <q-tab-panel name="innerprivacy">
                  <q-editor
                    v-model="privacyContent"
                    :disable="isLoading === true"
                    :toolbar="toolbarItems"
                    min-height="15em"
                    paragraph-tag="p"
                    height="calc(100vh - 340px)"
                    @update:model-value="savePage('privacy', privacyContent)"
                  />
                </q-tab-panel>
              </q-tab-panels>
            </template>
          </q-splitter>
        </q-scroll-area>
      </q-card-section>
    </q-card>
  </section>
</template>

<script setup lang="ts">
import {
  barStyle,
  privacyPage,
  thumbStyle,
  tosPage,
  useDynamicPageStore,
} from 'src/entities/setting';
import { ref } from 'vue';
import { useQuasar } from 'quasar';

// Refs for reactive variables
const isLoading = ref(false);
const innerTab = ref('innertos');
const splitterModel = ref(10);
const $q = useQuasar();
const dynamicPageStore = useDynamicPageStore();

// Async refs for loading page content
const tosContent = ref(await tosPage());
const privacyContent = ref(await privacyPage());

/**
 * Saves the content of a page.
 * @param {string} pageName - The name of the page to save.
 * @param {string} content - The content to save on the page.
 */
const savePage = async (pageName: string, content: string): Promise<void> => {
  try {
    await dynamicPageStore.savePageByPageName(pageName, content);
  } catch (err) {
    console.error('Error saving dynamic page:', err);
  }
};

// Toolbar items configuration for a text editor
const toolbarItems = [
  ['undo', 'redo'],
  [
    'bold',
    'italic',
    'strike',
    'underline',
    'subscript',
    'superscript',
    {
      label: $q.lang.editor.align,
      icon: $q.iconSet.editor.align,
      fixedLabel: true,
      list: 'only-icons',
      options: ['left', 'center', 'right', 'justify'],
    },
  ],
  ['token', 'hr', 'link', 'custom_btn'],
  ['print', 'fullscreen'],
  [
    {
      label: $q.lang.editor.formatting,
      icon: $q.iconSet.editor.formatting,
      list: 'no-icons',
      options: ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'code'],
    },
    {
      label: $q.lang.editor.fontSize,
      icon: $q.iconSet.editor.fontSize,
      fixedLabel: true,
      fixedIcon: true,
      list: 'no-icons',
      options: [
        'size-1',
        'size-2',
        'size-3',
        'size-4',
        'size-5',
        'size-6',
        'size-7',
      ],
    },
    'removeFormat',
  ],
  ['quote', 'unordered', 'ordered', 'outdent', 'indent'],
  ['viewsource'],
];
</script>
<style scoped lang="scss"></style>
