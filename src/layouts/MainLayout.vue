<template>
  <q-layout
    view="hHh lpR fFf"
    :class="{
      'bg-white': $q.screen.lt.sm,
    }"
  >
    <q-header
      :elevated="isTopNavDefault"
      :class="{
        'bg-primary text-white': isTopNavDefault,
        'bg-transparent no-shadow text-black': !isTopNavDefault,
      }"
      :style="isTopNavDefault ? 'position: fixed' : 'position: relative'"
    >
      <q-toolbar
        class="justify-between text-white q-pl-none"
        :class="{
          'bg-transparent no-shadow q-pa-md': !isTopNavDefault,
        }"
      >
        <div class="header-rows items-center row">
          <q-btn
            :stretch="isTopNavDefault"
            :color="!isTopNavDefault ? 'primary' : ''"
            :flat="isTopNavDefault"
            :rounded="!isTopNavDefault"
            :padding="!isTopNavDefault ? '13px' : ''"
            icon="home"
            to="/books"
            v-if="
              !['booklist', 'main'].includes(route.name) &&
              (isAuthor || isAdmin || isReviewer)
            "
          >
            &nbsp;&nbsp;Books</q-btn
          >
          <div id="new-book-create"></div>
          <div id="book-actions">
            <!-- This will be filled by the book editing component -->
          </div>
          <q-separator vertical v-if="isTopNavDefault" />
          <div id="header-title">
            <!-- This will be filled by the book editing component -->
          </div>
        </div>
        <div class="header-rows items-center row">
          <div class="q-mr-sm text-black">
            <q-chip
              v-if="isReviewMode"
              :outline="$q.screen.gt.sm"
              color="primary"
              text-color="white"
              :icon="$q.screen.gt.sm ? 'preview' : 'visibility'"
              style="font-size: 1.2em"
            >
              <span v-if="$q.screen.gt.sm">
                {{ user.displayName || user.email }}
                <span v-if="reviewing !== null"> &nbsp;{{ reviewing }}</span>
              </span>
              <span v-else>
                <span v-if="$q.screen.sm"> Review Mode </span>
                <q-tooltip>
                  {{ user.displayName || user.email }}
                  <span v-if="reviewing !== null"> &nbsp;{{ reviewing }}</span>
                </q-tooltip>
              </span>
            </q-chip>
          </div>
          <div class="q-mr-sm" v-if="loginAs && browserWidth > 767">
            <q-btn
              :class="isTopNavDefault ? '' : 'q-ml-xs'"
              :dense="isTopNavDefault"
              :stretch="isTopNavDefault"
              :color="!isTopNavDefault ? 'primary' : 'white'"
              :flat="isTopNavDefault"
              :rounded="!isTopNavDefault"
              :padding="!isTopNavDefault ? '13px' : ''"
              v-if="loggingAs !== null"
              to="/setting-users"
              icon="close"
            >
              EXIT LOGIN-AS
              <q-tooltip>Exit {{ loggingAs }}</q-tooltip>
            </q-btn>
          </div>
          <div id="review-status">
            <!-- This will be filled by the book editing component -->
          </div>
          <div id="right-drawer"></div>
          <Suspense>
            <TopBarNotifications />
          </Suspense>
          <q-btn
            :class="isTopNavDefault ? '' : 'q-mr-xs'"
            :dense="isTopNavDefault"
            :stretch="isTopNavDefault"
            :color="!isTopNavDefault ? 'primary' : ''"
            :flat="isTopNavDefault"
            :round="!isTopNavDefault"
            :padding="!isTopNavDefault ? '13px' : ''"
            v-if="!$q.screen.lt.md"
            @click="$q.fullscreen.toggle()"
            :icon="$q.fullscreen.isActive ? 'fullscreen_exit' : 'fullscreen'"
          >
            <q-tooltip>{{
              $q.fullscreen.isActive ? 'Exit Full Screen' : 'Enter Full Screen'
            }}</q-tooltip>
          </q-btn>
          <q-btn
            :dense="isTopNavDefault"
            :stretch="isTopNavDefault"
            :color="!isTopNavDefault ? 'primary' : ''"
            :flat="isTopNavDefault"
            :round="!isTopNavDefault"
            :padding="!isTopNavDefault ? '13px' : ''"
            icon="person"
          >
            <q-menu>
              <q-list>
                <q-item v-if="isAdmin" to="/setting-users" clickable v-ripple>
                  <q-item-section avatar>
                    <q-icon name="group" />
                  </q-item-section>
                  <q-item-section>Users</q-item-section>
                </q-item>
                <q-item
                  v-if="1 != 1"
                  to="/setting-book-category"
                  clickable
                  v-ripple
                >
                  <q-item-section avatar>
                    <q-icon name="category" />
                  </q-item-section>
                  <q-item-section>Book Categories</q-item-section>
                </q-item>
                <q-item v-if="isAdmin" to="/setting-pages" clickable v-ripple>
                  <q-item-section avatar>
                    <q-icon name="edit_note" />
                  </q-item-section>
                  <q-item-section>Pages (CMS)</q-item-section>
                </q-item>
                <q-separator />
                <q-item clickable v-ripple to="/">
                  <q-item-section avatar>
                    <q-icon name="home" />
                  </q-item-section>
                  <q-item-section>Home</q-item-section>
                </q-item>
                <q-item clickable v-ripple to="/profile">
                  <q-item-section avatar>
                    <q-icon name="verified" />
                  </q-item-section>
                  <q-item-section>Subscriptions</q-item-section>
                </q-item>
                <q-separator />
                <q-item
                  clickable
                  v-close-popup
                  v-ripple
                  @click="handleHelpClick"
                >
                  <q-item-section avatar>
                    <q-icon name="support" />
                  </q-item-section>
                  <q-item-section>Help</q-item-section>
                </q-item>
                <q-item clickable v-ripple to="/tos">
                  <q-item-section avatar>
                    <q-icon name="fact_check" />
                  </q-item-section>
                  <q-item-section>Terms of Service</q-item-section>
                </q-item>
                <q-separator />
                <q-item clickable v-ripple v-close-popup @click="logOut()">
                  <q-item-section avatar>
                    <q-icon name="logout" />
                  </q-item-section>
                  <q-item-section>Logout</q-item-section>
                </q-item>
              </q-list>
            </q-menu>
          </q-btn>
        </div>
      </q-toolbar>

      <div id="page-main-loader" class="absolute-top">
        <q-linear-progress
          indeterminate
          dark
          color="primary"
          v-if="loadingState"
        />
      </div>
    </q-header>
    <q-page-container
      :style="
        !isTopNavDefault ? 'margin-top: -10px; padding-top: 0px !important' : ''
      "
    >
      <q-inner-loading
        :showing="loadingState"
        color="primary"
        label-class="text-primary"
        label-style="font-size: 1.1em"
        style="z-index: 1001"
      >
        <div class="fixed-center">
          &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          <q-spinner-dots color="primary" size="2em" />
          <div class="align-center">
            <q-icon name="img:robot.png" class="q-ml-xs" size="xl" />
            Hang tight, Loading....
          </div>
        </div>
      </q-inner-loading>
      <Suspense>
        <router-view />
      </Suspense>
    </q-page-container>

    <q-page-sticky
      position="bottom-right"
      :offset="[18, 18]"
      v-show="showhelpDialogForm"
    >
      <q-fab
        label="Help"
        label-position="left"
        color="primary"
        icon="support"
        direction="left"
        v-model="helpDialogForm"
      >
        <q-fab-action class="help-popup" disable v-if="browserWidth >= 600">
          <HelpCard
            @minimizeToggle="
              helpDialogForm = false;
              showhelpDialogForm = true;
            "
            @closeToggle="showhelpDialogForm = false"
          />
        </q-fab-action>
        <q-fab-action
          color="purple"
          v-else
          icon="link"
          label="Help"
          to="/help"
        />
      </q-fab>
    </q-page-sticky>
  </q-layout>

  <q-dialog v-model="tosModal" persistent>
    <q-card style="min-width: 70vw">
      <q-card-section class="q-pa-none">
        <q-bar class="bg-primary text-white">
          <q-icon name="img:robot.png" />

          <div>Welcome to Manuscriptr!</div>
        </q-bar>
      </q-card-section>
      <q-card-section class="q-pa-none">
        <TOS pageHeight="650" pageWidth="100%" />
      </q-card-section>
      <q-separator />
      <q-card-section class="q-pa-none">
        <PrivacyPolicy pageHeight="650" pageWidth="100%" />
      </q-card-section>
      <q-separator />
      <q-card-actions align="between">
        <q-checkbox
          right-label
          v-model="iAgreeCheck"
          label="I agree to the Terms and Conditions and Privacy Policy."
          checked-icon="task_alt"
          unchecked-icon="highlight_off"
        />
        <q-btn
          color="primary"
          :disable="!iAgreeCheck"
          icon-right="check"
          @click="clickIagree"
        >
          Submit and Continue
        </q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { auth } from 'src/firebase';
import { watch, onMounted, ref, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { User, signOut } from 'firebase/auth';
import { useQuasar } from 'quasar';
import {
  HelpCard,
  isAdmin,
  isAuthor,
  isReviewer,
  isReviewMode,
  loadingState,
  loginAs,
  user,
  useAuthorStore,
  useSubscriptionStore,
  isLoggedIn,
  useAuthStore,
} from 'src/entities/user';
import {
  isMaintenanceMode,
  isTopNavDefault,
  useBookSettingsStore,
} from 'src/entities/setting';
import { computedAsync } from '@vueuse/core';
import { TOS, PrivacyPolicy } from 'src/entities/setting';
import { serverTimestamp } from 'firebase/firestore';
import {
  PushNotificationService,
  TopBarNotifications,
  useNotificationStore,
} from 'src/entities/notification';

// Reactive references for browser width and div size
const browserWidth = ref(window.innerWidth);

/** Function to update the browser width */
const updateWidth = (): void => {
  browserWidth.value = window.innerWidth;
};

// Vue Router and Vuex store instances
const router = useRouter();
const route = useRoute();
const $q = useQuasar();
const helpDialogForm = ref(true);
const showhelpDialogForm = ref(false);
const iAgreeCheck = ref(false);
const tosModal = ref(false);
const authorStore = useAuthorStore();
const authStore = useAuthStore();
const subscriptionStore = useSubscriptionStore();
const bookSettingStore = useBookSettingsStore();
const notificationStore = useNotificationStore();

/** Clean up event listeners and observers on component unmount */
onUnmounted(() => {
  window.removeEventListener('resize', updateWidth);
});

/**
 * Computed property to handle review mode display.
 */
const reviewing = computedAsync(async () => {
  if (!isReviewMode.value) return null;

  const author = await authorStore.getAuthorWithoutSync(isReviewMode.value);
  return `is reviewing ${author.name}`;
}, null);

/**
 * Computed property to handle logging as another user.
 */
const loggingAs = computedAsync(async () => {
  if (!loginAs.value) return null;

  const author = await authorStore.getAuthorWithoutSync(loginAs.value);
  return `LoginAs ${author.name || author.email}`;
}, null);

/**
 * Display a subscription notification.
 * @param message - Notification message.
 * @param color - Notification color.
 * @param url - URL to redirect to.
 * @param url_label - Label for the URL action.
 */
const subscriptionNotifs = (
  message: string,
  color: string,
  url: string,
  url_label: string,
): void => {
  const subscribeNotif = $q.notify({
    message,
    color,
    multiLine: true,
    avatar: 'robot.png',
    actions: [
      {
        label: url_label,
        color: 'yellow',
        handler: () => {
          subscribeNotif();
          router.push(url);
        },
      },
    ],
  });
};

/** Log out the current user. */
const logOut = async (): void => {
  unSubs();
  await authStore.logOut();
};

/** Unsubs snapshots. */
const unSubs = (): void => {
  isReviewMode.value = '';
  loginAs.value = '';
  authorStore.stopListeningUserLoggedIn();
  subscriptionStore.cleanupListeners();
  bookSettingStore.cleanupListeners();
};
/** Handle help dialog display based on conditions */
const handleHelpClick = (): void => {
  if (browserWidth.value < 600 || route.path.startsWith('/help')) {
    router.push('/help');
  } else {
    showhelpDialogForm.value = !showhelpDialogForm.value;
    helpDialogForm.value = true;
  }
};

// Watchers
watch(subscriptionStore, (newVal) => {
  if (!newVal.isSubscribe) {
    notificationStore.createUserNotification(user?.value?.uid as string, {
      title: `Account Subscription Cancelled `,
      notification: `<p>You no longer have an active subscription.</p>`,
      url: `/profile`,
    });
    subscriptionNotifs(
      'You no longer have an active subscription.',
      'red',
      '/',
      'Subscribe Now!',
    );
  }
});
watch(isMaintenanceMode, (newVal) => {
  if (newVal && route.meta?.maintenanceMode !== false) {
    window.location.href = '/maintenance';
  }
});

// Lifecycle hooks
onMounted(async () => {
  unSubs();
  if (route.path.startsWith('/help')) {
    showhelpDialogForm.value = false;
  }
  window.addEventListener('resize', updateWidth);
  if (isLoggedIn.value) {
    console.log('Logged in.');
    await checkIfNewUser(user?.value as User);
    await getFcmToken(user?.value as User);
  }
});
onUnmounted(() => {
  unSubs();
});

/**
 * Retrieves and manages the FCM token for push notifications.
 * @param result The user object containing user details.
 */
const getFcmToken = async (result: User): Promise<void> => {
  // Check if the user has a valid UID
  if (result.uid) {
    // Request permission for notifications if no token exists
    await PushNotificationService.requestPermission(result.uid);
  }

  // Listen for incoming push messages
  PushNotificationService.listenForMessages();
};

/** Check if the user is new and handle first sign-in logic */
const checkIfNewUser = async (result: User): Promise<void> => {
  if (!result.uid)
    throw new Error('User could not be found. Please try again!');
  const userLogged = await authorStore.getAuthorWithoutSync(result.uid);
  if (userLogged && !userLogged?.firstSignIn && !isAdmin.value) {
    tosModal.value = true;
  }
};

/** Handle user agreement action */
const clickIagree = async (): Promise<void> => {
  tosModal.value = false;
  await authorStore.updateAuthor(user.value?.uid as string, {
    firstSignIn: serverTimestamp(),
  });
  router.push('/');
};
</script>

<style lang="scss">
.help-popup {
  align-items: self-end;
  justify-content: center;
  align-self: self-end;
  border-radius: 0;
  padding: 0;
  &.q-btn.disabled {
    opacity: 1 !important;
    cursor: default !important;
    * {
      cursor: default !important;
    }
  }
}
.selected-link .q-focus-helper {
  background: #fff !important;
  opacity: 0.15 !important;
}
.header-rows {
  height: 50px;
}

.header-mode-boxes {
  padding: 4px 8px;
  background: white;
  font-weight: bold;
  border-radius: 4px;
  cursor: pointer;
}
.force-justify {
  justify-content: flex-end !important;
}

#left-panel .q-toolbar {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.hide-separator {
  display: none;
}

#header-title {
  padding: 0px 16px;
}
</style>
