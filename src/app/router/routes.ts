import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    component: () => import('pages/Login.vue'),
    meta: { requiresAuth: false, maintenanceMode: true },
  },
  {
    path: '/admin-login',
    component: () => import('pages/Login.vue'),
    meta: { requiresAuth: false, maintenanceMode: false },
  },
  {
    path: '/maintenance',
    component: () => import('pages/Maintenance.vue'),
    meta: {
      requiresAuth: false,
      maintenanceMode: false,
    },
  },
  {
    path: '/',
    component: () => import('layouts/MainLayout.vue'),
    meta: { requiresAuth: true, maintenanceMode: true },
    children: [
      { name: 'main', path: '', component: () => import('pages/Main.vue') },
      { name: 'help', path: 'help', component: () => import('pages/Help.vue') },
      {
        path: '',
        children: [
          {
            path: 'books',
            name: 'booklist',
            component: () => import('pages/BooksList.vue'),
            meta: { requiresAuthor: true },
          },
          {
            meta: { requiresAuthor: true, maintenanceMode: false },
            path: 'books/:id',
            name: 'bookdetails',
            component: () => import('pages/BookDetails.vue'),
          },
          {
            meta: { requiresAuthor: true },
            path: 'books/:id/export',
            name: 'bookexport',
            component: () => import('pages/BookExport.vue'),
          },
          {
            meta: { requiresAuthor: true },
            path: 'books/:id/versions',
            name: 'bookversion',
            component: () => import('pages/BookVersions.vue'),
          },
        ],
      },
      {
        path: 'setting-book-category',
        name: 'bookcategory',
        component: () => import('pages/BookCategory.vue'),
        meta: { requiresAuthor: true },
      },
      {
        path: 'notifications',
        name: 'usersnotifications',
        component: () => import('pages/UserNotifications.vue'),
        meta: { requiresAuthor: true },
      },
      {
        path: 'setting-users',
        name: 'userslist',
        component: () => import('pages/Users.vue'),
        meta: { requiresAdmin: true },
      },
      {
        path: 'setting-pages',
        name: 'dynamicpages',
        component: () => import('pages/DynamicPages.vue'),
        meta: { requiresAdmin: true },
      },
      { path: 'profile', component: () => import('pages/Profile.vue') },
      { path: 'packages', component: () => import('pages/Packages.vue') },
      { path: 'tos', component: () => import('pages/TOS.vue') },
      {
        path: 'privacy-policy',
        component: () => import('pages/PrivacyPolicy.vue'),
      },
    ],
  },
  {
    name: 'error-404',
    path: '/:catchAll(.*)*',
    component: () => import('pages/Error404.vue'),
  },
];

export default routes;
