import { Dialog, DialogChainObject } from 'quasar';
import {
  onOpenAIButtonsApiCall,
  pollApiWithProgress,
  StreamingDialog,
  PromptId,
} from 'src/features/book-prompts/index';

export async function onOpenAIButtons(
  promptId: PromptId,
  content: string,
  bookId: string,
): Promise<string | false> {
  const prompt = await onOpenAIButtonsApiCall(promptId, content, bookId);

  if (prompt.prompt as string) {
    return new Promise((resolve) => {
      const dialog = Dialog.create({
        component: StreamingDialog,
        componentProps: {
          promptId: promptId,
          prompt: prompt.prompt as string,
          content: '',
          isComplete: false,
        },
        persistent: true,
      })
        .onOk((responseData: string) => {
          // User confirmed to use the response
          resolve(responseData);
        })
        .onCancel(() => {
          // User cancelled
          resolve(false);
        })
        .onDismiss(() => {
          // Dialog was dismissed
          resolve(false);
        });
    });
  } else {
    return prompt.prompt as string;
  }
}
