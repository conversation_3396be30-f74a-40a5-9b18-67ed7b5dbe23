import { Dialog, DialogChainObject } from 'quasar';
import {
  onOpenAIButtonsApiCall,
  pollApiWithProgress,
  StreamingDialog,
  PromptId,
} from 'src/features/book-prompts/index';

export async function onOpenAIButtons(
  promptId: PromptId,
  content: string,
  bookId: string,
) {
  let dialog: DialogChainObject | null = null;
  const prompt = await onOpenAIButtonsApiCall(promptId, content, bookId);

  if (prompt.prompt as string) {
    dialog = Dialog.create({
      component: StreamingDialog,
      componentProps: {
        content: '',
        isComplete: false,
      },
      persistent: true,
    });

    return pollApiWithProgress(
      prompt.promptId as PromptId,
      prompt.prompt as string,
      (content) => {
        console.log('content========', content);
        console.log('dialog========', dialog);
        dialog?.update({
          componentProps: {
            content: content,
            isComplete: false,
          },
        });
      },
    )
      .then((responseData) => {
        dialog?.update({
          componentProps: {
            content: responseData,
            isComplete: true,
          },
        });
        return responseData;
      })
      .catch((error) => {
        dialog?.hide();
        console.error('Error while polling API:', error);
        return false;
      });
  } else {
    return prompt.prompt as string;
  }
}
