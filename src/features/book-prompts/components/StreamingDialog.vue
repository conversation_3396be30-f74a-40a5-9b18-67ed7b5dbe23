<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide">
    <q-card style="width: 600px; max-width: 80vw">
      <q-card-section class="row items-center no-wrap">
        <div class="q-mr-sm">
          <q-img src="robot.png" width="42px" />
        </div>
        <div id="prompt-loader" class="instruct-dialog-title align-center">
          <PERSON> is writing, hang tight!
        </div>
      </q-card-section>

      <q-card-section class="prompt-content">
        <div v-html="contentStream"></div>
        <span class="cursor" v-if="!isComplete"></span>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn
          flat
          label="Done"
          color="primary"
          v-close-popup
          :disabled="!isComplete"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { useDialogPluginComponent } from 'quasar';
import { ref, watch } from 'vue';

const props = defineProps({
  content: {
    type: String,
    default: '',
  },
  isComplete: {
    type: Boolean,
    default: false,
  },
});

const contentStream = ref(props.content);

defineEmits(useDialogPluginComponent.emits);

const { dialogRef, onDialogCancel, onDialogHide, onDialogOK } =
  useDialogPluginComponent();

watch(
  () => props.content,
  (newContent) => {
    console.log('newContent========', newContent);
    contentStream.value = newContent;
  },
);
</script>

<style scoped>
.prompt-content {
  min-height: 150px;
  white-space: pre-wrap;
  word-wrap: break-word;
  background-color: #f5f5f5;
  border-radius: 5px;
  padding: 15px;
  margin: 0 16px;
}

.cursor {
  display: inline-block;
  width: 10px;
  height: 1.2em;
  background-color: black;
  animation: blink 1s step-end infinite;
  vertical-align: bottom;
}

@keyframes blink {
  50% {
    opacity: 0;
  }
}
</style>
