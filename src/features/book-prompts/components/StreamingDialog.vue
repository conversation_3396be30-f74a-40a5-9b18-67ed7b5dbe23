<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" persistent>
    <q-card style="width: 700px; max-width: 90vw" class="streaming-dialog">
      <q-card-section class="dialog-header">
        <div class="header-content">
          <div class="robot-avatar">
            <q-img src="robot.png" width="48px" />
            <div class="pulse-ring" v-if="!isComplete"></div>
          </div>
          <div class="header-text">
            <div class="title">{{ headerTitle }}</div>
            <div class="subtitle" v-if="!isComplete && !hasError">
              <span class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
              </span>
              Generating response...
            </div>
            <div class="subtitle success" v-else-if="isComplete && !hasError">
              <q-icon name="check_circle" size="16px" class="q-mr-xs" />
              Response generated successfully
            </div>
            <div class="subtitle error" v-else-if="hasError">
              <q-icon name="error" size="16px" class="q-mr-xs" />
              Something went wrong
            </div>
          </div>
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section class="content-section">
        <div class="content-container">
          <div class="content-text" ref="contentRef">
            <div v-html="displayedContent"></div>
            <span
              class="modern-cursor"
              v-if="!isComplete && displayedContent"
            ></span>
          </div>
          <div
            v-if="!displayedContent && !isComplete"
            class="loading-placeholder"
          >
            <div class="loading-line"></div>
            <div class="loading-line short"></div>
            <div class="loading-line medium"></div>
          </div>
        </div>
      </q-card-section>

      <q-separator v-if="isComplete" />

      <q-card-actions v-if="isComplete" align="right" class="action-buttons">
        <q-btn
          flat
          :label="hasError ? 'Close' : 'Cancel'"
          color="grey-7"
          @click="onCancel"
          class="cancel-btn"
        />
        <q-btn
          v-if="!hasError"
          unelevated
          label="Use Response"
          color="primary"
          @click="onUseResponse"
          class="use-btn"
          icon="check"
        />
        <q-btn
          v-else
          unelevated
          label="Try Again"
          color="orange"
          @click="onTryAgain"
          class="retry-btn"
          icon="refresh"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { useDialogPluginComponent } from 'quasar';
import { ref, watch, computed, nextTick, onMounted } from 'vue';
import { pollApiWithProgress, type PromptId } from '../lib/utils';
import type { PromptId as PromptIdType } from '../model';

const props = defineProps({
  promptId: {
    type: String as () => PromptIdType,
    required: true,
  },
  prompt: {
    type: String,
    required: true,
  },
  content: {
    type: String,
    default: '',
  },
  isComplete: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits([
  ...useDialogPluginComponent.emits,
  'response-confirmed',
  'response-cancelled',
]);

const { dialogRef, onDialogCancel, onDialogHide, onDialogOK } =
  useDialogPluginComponent();

const contentStream = ref('');
const displayedContent = ref('');
const isComplete = ref(false);
const contentRef = ref<HTMLElement>();
const isStreaming = ref(false);
const hasError = ref(false);
const errorMessage = ref('');

const headerTitle = computed(() => {
  if (hasError.value) return 'Error Occurred';
  if (isComplete.value) return 'Response Complete';
  if (isStreaming.value) return 'Manny is Writing';
  return 'Preparing Response';
});

// Typing effect variables
let typingInterval: NodeJS.Timeout | null = null;
let currentIndex = 0;

const startTypingEffect = (content: string) => {
  if (typingInterval) {
    clearInterval(typingInterval);
  }

  currentIndex = 0;
  displayedContent.value = '';

  typingInterval = setInterval(() => {
    if (currentIndex < content.length) {
      displayedContent.value = content.substring(0, currentIndex + 1);
      currentIndex++;

      // Auto-scroll to bottom
      nextTick(() => {
        if (contentRef.value) {
          contentRef.value.scrollTop = contentRef.value.scrollHeight;
        }
      });
    } else {
      if (typingInterval) {
        clearInterval(typingInterval);
        typingInterval = null;
      }
    }
  }, 20); // Adjust speed as needed
};

const startStreaming = async () => {
  try {
    isStreaming.value = true;

    await pollApiWithProgress(
      props.promptId as PromptIdType,
      props.prompt,
      (content) => {
        contentStream.value = content;
        startTypingEffect(content);
      },
    );

    isComplete.value = true;
    isStreaming.value = false;
  } catch (error) {
    console.error('Streaming error:', error);
    hasError.value = true;
    isComplete.value = true;
    isStreaming.value = false;
    errorMessage.value =
      error instanceof Error ? error.message : 'An unexpected error occurred';
    displayedContent.value = `<div style="color: #ef4444; padding: 16px; background: #fef2f2; border-radius: 8px; border: 1px solid #fecaca;">
      <strong>Error:</strong> ${errorMessage.value}
      <br><br>
      Please try again or contact support if the issue persists.
    </div>`;
  }
};

const onUseResponse = () => {
  emit('response-confirmed', contentStream.value);
  onDialogOK(contentStream.value);
};

const onCancel = () => {
  emit('response-cancelled');
  onDialogCancel();
};

const onTryAgain = () => {
  // Reset state and try again
  hasError.value = false;
  isComplete.value = false;
  isStreaming.value = false;
  contentStream.value = '';
  displayedContent.value = '';
  errorMessage.value = '';
  startStreaming();
};

// Start streaming when component mounts
onMounted(() => {
  startStreaming();
});

// Cleanup on unmount
const cleanup = () => {
  if (typingInterval) {
    clearInterval(typingInterval);
    typingInterval = null;
  }
};

// Watch for dialog hide to cleanup
watch(
  () => dialogRef.value,
  (newVal) => {
    if (!newVal) {
      cleanup();
    }
  },
);
</script>

<style scoped lang="scss">
.streaming-dialog {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.dialog-header {
  /** background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); **/
  background: $primary;
  color: white;
  padding: 20px 24px;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.robot-avatar {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pulse-ring {
  position: absolute;
  width: 60px;
  height: 60px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

.header-text {
  flex: 1;
}

.title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.subtitle {
  font-size: 14px;
  opacity: 0.9;
  display: flex;
  align-items: center;
}

.subtitle.success {
  color: #4ade80;
}

.subtitle.error {
  color: #ef4444;
}

.typing-dots {
  display: inline-flex;
  gap: 2px;
  margin-right: 8px;
}

.typing-dots span {
  width: 4px;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}
.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.content-section {
  padding: 0;
  max-height: 60vh;
  overflow: hidden;
}

.content-container {
  min-height: 200px;
  max-height: 60vh;
  overflow-y: auto;
  padding: 24px;
  background: #fafafa;
}

.content-text {
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
  font-size: 15px;
  line-height: 1.6;
  color: #374151;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.modern-cursor {
  display: inline-block;
  width: 2px;
  height: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin-left: 2px;
  animation: modernBlink 1s infinite;
  vertical-align: text-bottom;
}

@keyframes modernBlink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

.loading-placeholder {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 20px 0;
}

.loading-line {
  height: 16px;
  background: linear-gradient(90deg, #e5e7eb 25%, #f3f4f6 50%, #e5e7eb 75%);
  background-size: 200% 100%;
  border-radius: 8px;
  animation: shimmer 2s infinite;
}

.loading-line.short {
  width: 60%;
}
.loading-line.medium {
  width: 80%;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.action-buttons {
  padding: 16px 24px;
  background: white;
  gap: 12px;
}

.cancel-btn {
  padding: 8px 20px;
  border-radius: 8px;
  font-weight: 500;
}

.use-btn {
  padding: 8px 20px;
  border-radius: 8px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.2s ease;
}

.use-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.retry-btn {
  padding: 8px 20px;
  border-radius: 8px;
  font-weight: 600;
  background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
  box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
  transition: all 0.2s ease;
}

.retry-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(249, 115, 22, 0.4);
}

/* Scrollbar styling */
.content-container::-webkit-scrollbar {
  width: 6px;
}

.content-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.content-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.content-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
