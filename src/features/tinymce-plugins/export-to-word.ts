import type TinyMC<PERSON> from 'tinymce';
import type { Editor } from 'tinymce';
import { Dialog } from 'quasar';
import {
  Book,
  BookOutlines,
  ChapterOutline,
  getChapter,
} from 'src/entities/book';
import { bookHtmlToDocx } from 'src/features/book-export';
import { unref } from 'vue';

export function createExportToWordPlugin(
  tinymce: typeof TinyMCE,
  book: Book,
  outline: BookOutlines,
  onUpdate?: () => void,
  onClick?: () => void,
) {
  tinymce.PluginManager.add('export-to-word', (editor: Editor) => {
    editor.ui.registry.addIcon(
      'exporttowordicon',
      '<svg viewBox="0 -1.27 110.031 110.031" xmlns="http://www.w3.org/2000/svg" fill="#000000" width="24" height="24"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M57.505 0h7.475v10c13.375.075 26.738-.138 40.101.075 2.85-.288 5.087 1.925 4.825 4.775.212 24.625-.05 49.262.125 73.887-.125 2.525.25 5.325-1.213 7.562-1.825 1.3-4.188 1.138-6.312 1.237-12.514-.061-25.014-.036-37.526-.036v10h-7.812c-19.024-3.475-38.1-6.662-57.162-10-.013-29.162 0-58.325 0-87.475C19.167 6.675 38.343 3.413 57.506 0z" fill="#2a5699"></path><path d="M64.98 13.75h41.25v80H64.98v-10h32.5v-5h-32.5V72.5h32.5v-5h-32.5v-6.25h32.5v-5h-32.5V50h32.5v-5h-32.5v-6.25h32.5v-5h-32.5V27.5h32.5v-5h-32.5v-8.75zM25.83 35.837c2.375-.137 4.75-.237 7.125-.362 1.662 8.438 3.362 16.862 5.162 25.262 1.413-8.675 2.976-17.325 4.487-25.987 2.5-.087 5-.225 7.488-.375-2.825 12.112-5.3 24.325-8.388 36.362-2.088 1.088-5.213-.05-7.688.125-1.663-8.274-3.6-16.5-5.088-24.812-1.462 8.075-3.362 16.075-5.037 24.101-2.4-.125-4.812-.275-7.226-.438-2.074-11-4.512-21.925-6.449-32.95 2.137-.1 4.287-.188 6.425-.263 1.287 7.962 2.75 15.888 3.875 23.862 1.765-8.174 3.564-16.349 5.314-24.525z" fill="#ffffff"></path></g></svg>',
    );

    editor.ui.registry.addMenuItem('exporttoword', {
      icon: 'exporttowordicon',
      text: 'Export to word',
      onAction: async () => {
        onClick?.();
        await exportToWord(book, outline, onUpdate);
      },
    });

    editor.ui.registry.addButton('exporttoword', {
      icon: 'exporttowordicon',
      tooltip: 'Export to word',
      onAction: async () => {
        onClick?.();
        await exportToWord(book, outline, onUpdate);
      },
    });
  });
}

async function exportToWord(
  book: Book,
  outline: BookOutlines,
  onUpdate?: () => void,
) {
  const saveNewVersion = await new Promise((resolve) => {
    Dialog.create({
      title: 'Export as a Word Document',
      message: 'Are you sure you want to export your book?',
      ok: {
        color: 'primary',
      },
      cancel: {
        color: 'negative',
      },
    })
      .onOk(() => resolve(true))
      .onCancel(() => resolve(false));
  });
  if (!saveNewVersion) {
    onUpdate?.();
    return;
  }

  try {
    const bookOutlines: ChapterOutline[] = [
      outline.introduction as ChapterOutline,
      ...(outline.outlines?.filter(
        (chapter) => !chapter.isSection,
      ) as ChapterOutline[]),
      outline.conclusion as ChapterOutline,
    ];
    const chapters = await Promise.all(
      bookOutlines.map((outline) => getChapter(book.id, outline.id)),
    );
    bookHtmlToDocx(book, bookOutlines, chapters.map(unref));
  } catch (e) {
    console.log(e);
    onUpdate?.();
  } finally {
    onUpdate?.();
  }
}
