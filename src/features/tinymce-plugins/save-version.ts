import type TinyMC<PERSON> from 'tinymce';
import type { Editor } from 'tinymce';
import { createBookVersion } from 'src/features/book-versioning';
import { Dialog, DialogChainObject } from 'quasar';

export function createSaveVersionPlugin(
  tinymce: typeof TinyMCE,
  bookId: string,
  onUpdate?: () => void,
  onClick?: () => void,
) {
  tinymce.PluginManager.add('save-version', (editor: Editor) => {
    editor.ui.registry.addIcon(
      'saveversionicon',
      '<svg fill="#000000" viewBox="0 0 1920 1920" xmlns="http://www.w3.org/2000/svg" width="24" height="24"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M790.706 338.824v112.94H395.412c-31.06 0-56.47 25.3-56.47 56.471v744.509c17.73-6.325 36.592-10.391 56.47-10.391h1129.412c19.877 0 38.738 4.066 56.47 10.39V508.236c0-31.171-25.412-56.47-56.47-56.47h-395.295V338.824h395.295c93.402 0 169.411 76.009 169.411 169.411v1242.353c0 93.403-76.01 169.412-169.411 169.412H395.412C302.009 1920 226 1843.99 226 1750.588V508.235c0-93.402 76.01-169.411 169.412-169.411h395.294Zm734.118 1016.47H395.412c-31.06 0-56.47 25.299-56.47 56.47v338.824c0 31.172 25.41 56.47 56.47 56.47h1129.412c31.058 0 56.47-25.298 56.47-56.47v-338.823c0-31.172-25.412-56.47-56.47-56.47ZM1016.622-.023v880.151l246.212-246.325 79.85 79.85-382.532 382.644-382.645-382.644 79.85-79.85L903.68 880.128V-.022h112.941ZM564.824 1468.235c-62.344 0-112.942 50.71-112.942 112.941s50.598 112.942 112.942 112.942c62.343 0 112.94-50.71 112.94-112.942 0-62.23-50.597-112.94-112.94-112.94Z" fill-rule="evenodd"></path> </g></svg>',
    );

    editor.ui.registry.addMenuItem('saveversion', {
      icon: 'saveversionicon',
      text: 'Save new version',
      onAction: async () => {
        onClick?.();
        await addVersion(bookId, onUpdate);
      },
    });

    editor.ui.registry.addButton('saveversion', {
      icon: 'saveversionicon',
      tooltip: 'Save new version',
      onAction: async () => {
        onClick?.();
        await addVersion(bookId, onUpdate);
      },
    });
  });
}

async function addVersion(bookId: string, onUpdate?: () => void) {
  const saveNewVersion = await new Promise((resolve) => {
    Dialog.create({
      title: 'Save New Version',
      message: 'Are you sure you want to save a new version?',
      ok: {
        color: 'primary',
      },
      cancel: {
        color: 'negative',
      },
    })
      .onOk(() => resolve(true))
      .onCancel(() => resolve(false));
  });
  if (!saveNewVersion) {
    onUpdate?.();
    return;
  }
  try {
    await createBookVersion(bookId as string);
  } catch (e) {
    onUpdate?.();
  } finally {
    onUpdate?.();
  }
}
