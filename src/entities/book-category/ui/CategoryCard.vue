<template>
  <q-card class="category-card shadow-2">
    <q-card-section class="bg-primary text-white">
      <div class="row items-center">
        <q-icon :name="category.icon" size="2em" />
        <div class="text-h6 q-ml-md">{{ category.title }}</div>
      </div>
    </q-card-section>

    <q-card-actions>
      <q-btn flat @click="$emit('edit')">Edit</q-btn>
      <q-btn flat @click="$emit('delete')">Delete</q-btn>
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import BookCategory from 'pages/BookCategory.vue';

defineProps<{
  category: BookCategory;
}>();

defineEmits<{
  (e: 'edit'): void;
  (e: 'delete'): void;
}>();
</script>
