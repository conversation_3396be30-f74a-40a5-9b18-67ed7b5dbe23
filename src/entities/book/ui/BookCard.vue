<template>
  <q-card class="column manny-book-card" style="height: 100%">
    <q-card-section class="col bg-primary text-white">
      <q-btn
        v-if="user?.uid === book?.authorId || isAdmin"
        style="float: right"
        round
        flat
        icon="more_vert"
      >
        <q-menu cover auto-close>
          <q-list>
            <q-item clickable @click="$emit('edit')" v-if="isAuthor || isAdmin">
              <q-item-section avatar>
                <q-icon name="edit" />
              </q-item-section>
              <q-item-section>Edit</q-item-section>
            </q-item>
            <q-item
              clickable
              @click="validateAccess(`/books/${book.id}/export`)"
            >
              <q-item-section avatar>
                <q-icon name="open_in_new" />
              </q-item-section>
              <q-item-section>Export</q-item-section>
            </q-item>
            <q-item
              clickable
              @click="validateAccess(`/books/${book.id}/versions`)"
              v-if="isAuthor || isAdmin"
            >
              <q-item-section avatar>
                <q-icon name="history" />
              </q-item-section>
              <q-item-section>Versions</q-item-section>
            </q-item>
            <q-item
              clickable
              @click="$emit('delete')"
              v-if="isAuthor || isAdmin"
            >
              <q-item-section avatar>
                <q-icon name="delete" color="red" />
              </q-item-section>
              <q-item-section>Delete</q-item-section>
            </q-item>
          </q-list>
        </q-menu>
      </q-btn>
      <p class="text-h5">{{ book.title || 'Untitled book' }}</p>
      <p class="text-subtitle2 q-mb-none">{{ book.subtitle }}</p>
    </q-card-section>
    <q-card-section>
      <p>Started at: {{ formatDate(book.createdAt) }}</p>
      <!-- <p>Last worked on: {{ formatDate(book.updatedAt) }}</p> -->
    </q-card-section>

    <q-separator />

    <q-card-section align="right">
      <div class="flex justify-between items-center">
        <q-icon
          size="1.4rem"
          color="primary"
          :name="
            book.category === 'autobiography'
              ? 'local_library'
              : book.category === 'faithstory'
              ? 'diversity_1'
              : 'collections_bookmark'
          "
        >
          <q-tooltip>{{ book.category || 'non-fiction' }}</q-tooltip>
        </q-icon>
        <q-btn flat @click="validateAccess('/books/' + book.id)">
          {{
            isReviewMode && book.authorId === isReviewMode ? 'Review' : 'Write'
          }}
        </q-btn>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import type { Book } from '../model/types';
import { date, useQuasar } from 'quasar';
import { isAdmin, isAuthor, isReviewMode, user } from 'src/entities/user';
import { toRef } from 'vue';
import { useRouter } from 'vue-router';

const props = defineProps<{
  book: Book;
}>();

defineEmits<{
  edit: [];
  delete: [];
}>();

const $q = useQuasar();
const router = useRouter();
const book = toRef(props, 'book');

const validateAccess = async (access: string) => {
  await router.push({ path: access });
};
function formatDate(timestamp: string | Date) {
  return date.formatDate(timestamp, 'M-D-YY h:mm A');
}
</script>
