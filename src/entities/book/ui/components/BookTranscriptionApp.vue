<template>
  <q-card
    class="transcription-app"
    style="
      max-width: 100vw;
      max-height: 90vh;
      display: flex;
      flex-direction: column;
    "
  >
    <!-- Modern Header -->
    <q-card-section class="app-header">
      <div class="header-content">
        <div class="robot-avatar">
          <q-img src="robot.png" width="40px" />
          <div class="pulse-ring" v-if="isRecording"></div>
        </div>
        <div class="header-text">
          <div class="title">{{ headerTitle }}</div>
          <div class="subtitle" v-if="isRecording">
            <span class="recording-indicator">
              <span></span>
              <span></span>
              <span></span>
            </span>
            Recording in progress...
          </div>
          <div class="subtitle" v-else-if="validating">
            <span class="typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </span>
            Preparing device...
          </div>
          <div
            class="subtitle success"
            v-else-if="transcriptionText.length > 0"
          >
            <q-icon name="check_circle" size="16px" class="q-mr-xs" />
            Transcription ready
          </div>
          <div class="subtitle" v-else>Ready to record</div>
        </div>
        <div class="header-actions">
          <!-- Settings Toggle -->
          <q-btn
            flat
            round
            size="sm"
            icon="settings"
            @click="toggleSettings"
            v-if="!isRecording"
            class="header-btn"
          />
          <!-- Close Button -->
          <q-btn
            flat
            round
            size="sm"
            icon="close"
            @click="emit('close')"
            class="header-btn"
          />
        </div>
      </div>
    </q-card-section>

    <!-- Compact Settings Panel -->
    <q-slide-transition>
      <q-card-section v-show="showSettings" class="settings-panel">
        <div class="settings-content">
          <div class="settings-header">
            <q-icon name="mic" size="18px" class="q-mr-xs" />
            <span class="settings-title">Microphone Settings</span>
          </div>
          <q-select
            v-model="selectedMicrophone"
            :options="microphoneOptions"
            label="Select Microphone"
            option-value="deviceId"
            option-label="label"
            emit-value
            map-options
            outlined
            dense
            class="settings-select"
            :disable="!isBrowserSupported || microphoneOptions.length === 0"
          >
            <template #prepend>
              <q-icon name="mic" />
            </template>
          </q-select>
        </div>
      </q-card-section>
    </q-slide-transition>

    <!-- Error State -->
    <q-card-section v-if="!isBrowserSupported" class="error-section">
      <div class="error-content">
        <q-icon name="error" size="48px" color="red-5" />
        <div class="error-text">
          <div class="error-title">Browser Not Supported</div>
          <div class="error-message">
            Your browser doesn't support microphone access or no microphone is
            available.
          </div>
        </div>
      </div>
    </q-card-section>

    <!-- Recording Interface -->
    <q-card-section v-else class="recording-section">
      <!-- Compact Recording Controls -->
      <div class="recording-controls">
        <!-- Audio Visualization -->
        <div class="visualization-container" v-if="isRecording">
          <AVMedia
            line-color="#667eea"
            audio-controls
            :media="mediaStream"
            type="line"
            :canv-height="40"
            :canv-width="$q.screen.gt.sm ? 300 : 200"
            canv-fill-color="#667eea"
            class="audio-visualizer"
          />
        </div>

        <!-- Ready State Message -->
        <div v-else class="ready-message">
          <q-icon name="mic" size="32px" color="primary" />
          <div class="ready-text">
            {{ validating ? 'Preparing device...' : 'Ready to record' }}
          </div>
        </div>

        <!-- Control Buttons -->
        <div class="control-buttons">
          <q-btn
            :icon="!isRecording ? 'mic' : 'stop'"
            :color="isRecording ? 'red-6' : 'primary'"
            @click="isRecording ? stopTranscription() : startTranscription()"
            :disable="validating"
            :loading="validating"
            size="lg"
            round
            class="record-btn"
          >
            <q-tooltip>{{
              isRecording ? 'Stop Recording' : 'Start Recording'
            }}</q-tooltip>
          </q-btn>

          <q-btn
            v-if="transcriptionText.length > 0"
            icon="visibility"
            @click="showTranscription = !showTranscription"
            :color="showTranscription ? 'teal-6' : 'grey-6'"
            round
            class="toggle-btn"
          >
            <q-tooltip>{{
              showTranscription ? 'Hide Text' : 'Show Text'
            }}</q-tooltip>
          </q-btn>
        </div>
      </div>

      <!-- Modern Transcription Display -->
      <q-slide-transition>
        <div v-show="showTranscription" class="transcription-container">
          <q-separator class="q-my-sm" />

          <!-- Live Transcription with Typing Effect -->
          <div v-if="isRecording" class="live-transcription">
            <div class="transcription-header">
              <q-icon name="record_voice_over" size="18px" class="q-mr-xs" />
              <span class="transcription-title">Live Transcription</span>
              <div class="live-indicator">
                <span class="live-dot"></span>
                LIVE
              </div>
            </div>
            <div class="transcription-content live">
              <div class="transcription-text" ref="liveContainer">
                <div v-html="displayedTranscription"></div>
                <span
                  class="modern-cursor"
                  v-if="displayedTranscription"
                ></span>
              </div>
              <div v-if="!displayedTranscription" class="waiting-message">
                <span class="typing-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </span>
                Listening for speech...
              </div>
            </div>
          </div>

          <!-- Editable Transcription -->
          <div v-else-if="transcriptionText.length" class="edit-transcription">
            <div class="transcription-header">
              <q-icon name="edit" size="18px" class="q-mr-xs" />
              <span class="transcription-title">Edit Transcription</span>
            </div>
            <div class="transcription-content">
              <q-input
                v-model="transcriptionText"
                type="textarea"
                placeholder="Your transcription will appear here..."
                outlined
                autogrow
                class="transcription-input"
                :input-style="{
                  minHeight: '120px',
                  fontSize: '15px',
                  lineHeight: '1.6',
                }"
              />
            </div>
          </div>
        </div>
      </q-slide-transition>

      <!-- Action Buttons -->
      <div v-if="transcriptionText.length > 0" class="action-buttons">
        <q-btn
          flat
          size="sm"
          label="Clear"
          color="grey-7"
          @click="clearText"
          icon="backspace"
          class="action-btn"
        />

        <q-space />

        <q-btn
          flat
          size="sm"
          label="Fix Grammar"
          color="orange-6"
          @click="fixGrammarUsingManny"
          :loading="loadingFixGrammar"
          :disable="loadingFixGrammar || isRecording"
          icon="auto_fix_high"
          class="action-btn"
        />

        <q-btn
          unelevated
          label="Use Transcription"
          color="primary"
          @click="useTranscription"
          :disable="!canUseContent || isRecording"
          icon="check"
          class="use-btn"
        />
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import {
  ref,
  watch,
  onUnmounted,
  onBeforeUnmount,
  nextTick,
  computed,
} from 'vue';
import RecordRTC, { StereoAudioRecorder } from 'recordrtc';
import { getSpeechRecognitionToken } from 'src/shared/api/speech';
import { composeText } from 'src/shared/api/openai';
import { useQuasar } from 'quasar';
import { AVMedia } from 'vue-audio-visual';
import { confirmOverrideText } from 'src/shared/lib/quasar-dialogs';

interface MediaDevice {
  deviceId: string;
  label: string;
}
interface MessageBase {
  audio_start: number;
  text: string;
  message_type: string;
}
interface MessagePartial extends MessageBase {
  message_type: 'PartialTranscript';
}
interface MessageFinal extends MessageBase {
  message_type: 'FinalTranscript';
  punctuated: boolean;
  text_formatted: boolean;
}
type TranscriptionMessage = MessagePartial | MessageFinal;

const $q = useQuasar();
const validating = ref<boolean>(false);
const selectedMicrophone = ref<string | null>(null);
const microphoneOptions = ref<MediaDevice[]>([]);
const isBrowserSupported = ref<boolean>(true);
const isRecording = ref<boolean>(false);
const canUseContent = ref<boolean>(false);
const loadingFixGrammar = ref<boolean>(false);
const transcriptionText = ref<string>('');
let socket: WebSocket | null = null;
let recorder: RecordRTC | null = null;
const showSettings = ref(false);
const audioBlobUrl = ref('');
const audioElement = ref<HTMLAudioElement | null>(null);
const displayText = ref('');
const fullText = ref('');
const typingIndex = ref(0);
const typingSpeed = 15; // milliseconds per character - faster for real-time feel
const mediaStream = ref<MediaStream | null>(null);
const showTranscription = ref<boolean>(false);

// Modern typing effect variables
const displayedTranscription = ref('');
const liveContainer = ref<HTMLElement>();
let typingQueue: string[] = [];
let isTyping = ref(false);
let typingTimeout: NodeJS.Timeout | null = null;

// Computed properties for modern UI
const headerTitle = computed(() => {
  if (isRecording.value) return 'Recording Audio';
  if (validating.value) return 'Preparing Device';
  if (transcriptionText.value.length > 0) return 'Transcription Complete';
  return 'Real-Time Transcription';
});

const emit = defineEmits<{
  completed: [content: string];
  close?: [];
}>();

// Modern real-time typing effect
const processTypingQueue = () => {
  if (typingQueue.length === 0 || isTyping.value) {
    return;
  }

  isTyping.value = true;
  const targetContent = typingQueue[typingQueue.length - 1]; // Get the latest content
  typingQueue = []; // Clear the queue

  const currentLength = displayedTranscription.value.length;
  const targetLength = targetContent.length;

  if (targetLength <= currentLength) {
    displayedTranscription.value = targetContent;
    isTyping.value = false;
    return;
  }

  // Type new characters one by one
  let currentIndex = currentLength;

  const typeNextChar = () => {
    if (currentIndex < targetLength) {
      displayedTranscription.value = targetContent.substring(
        0,
        currentIndex + 1,
      );
      currentIndex++;

      // Auto-scroll to bottom
      nextTick(() => {
        if (liveContainer.value) {
          liveContainer.value.scrollTop = liveContainer.value.scrollHeight;
        }
      });

      // Continue typing with a small delay
      typingTimeout = setTimeout(typeNextChar, typingSpeed);
    } else {
      isTyping.value = false;
      // Process any new content that arrived while typing
      if (typingQueue.length > 0) {
        setTimeout(processTypingQueue, 10);
      }
    }
  };

  typeNextChar();
};

const addToTypingQueue = (content: string) => {
  typingQueue.push(content);
  if (!isTyping.value) {
    processTypingQueue();
  }
};

// Legacy typing function for compatibility
const typeCharacter = async () => {
  while (typingIndex.value < fullText.value.length) {
    displayText.value += fullText.value[typingIndex.value];
    typingIndex.value++;
    await new Promise((resolve) => setTimeout(resolve, typingSpeed));
  }
};

const canvasWidth = ref<number>(500);
const container = ref<HTMLElement | null>(null);
const typewriterContainer = ref<HTMLElement | null>(null);

// Cleanup
onUnmounted(async () => {
  await nextTick();
  cleanupTyping();
  updateCanvasWidth();
  scrollToBottom();
  if (audioBlobUrl.value) URL.revokeObjectURL(audioBlobUrl.value);
  if (audioElement.value) audioElement.value = null;

  window.removeEventListener('resize', updateCanvasWidth);
});

const scrollToBottom = () => {
  if (typewriterContainer.value) {
    // Smooth scroll (optional)
    typewriterContainer.value.scrollTop =
      typewriterContainer.value.scrollHeight;
  }
};
const updateCanvasWidth = () => {
  if (container.value) {
    canvasWidth.value = container.value.offsetWidth;
  }
};

// Optional: Watch for recording state change
watch(
  () => isRecording.value,
  (newVal) => {
    if (newVal) {
      // Wait for DOM to update before accessing container
      setTimeout(() => {
        updateCanvasWidth();
      }, 0);
    }
  },
);
watch(
  () => transcriptionText.value,
  () => {
    // Use nextTick to wait for DOM update
    setTimeout(() => {
      scrollToBottom();
    }, 0);
  },
);

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateCanvasWidth);
});
// Existing functions remain unchanged
const useTranscription = () => {
  emit('completed', { content: transcriptionText.value });
};

const requestMicrophoneAccess = async (): Promise<void> => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    stream.getTracks().forEach((track) => track.stop());
    await fetchMicrophones();
  } catch (error) {
    console.warn('Microphone permission denied or not available.', error);
    isBrowserSupported.value = false;
    microphoneOptions.value = [];
  }
};

const fetchMicrophones = async (): Promise<void> => {
  try {
    const devices = await navigator.mediaDevices.enumerateDevices();
    const audioInputDevices = devices.filter(
      (device) => device.kind === 'audioinput',
    );
    microphoneOptions.value = audioInputDevices.map((device, index) => ({
      deviceId: device.deviceId,
      label: device.label || `Microphone ${index + 1}`,
    }));
    if (microphoneOptions.value.length > 0 && !selectedMicrophone.value) {
      selectedMicrophone.value = microphoneOptions.value[0].deviceId;
    }
    isBrowserSupported.value = true;
  } catch (error) {
    console.error('Error enumerating devices:', error);
    isBrowserSupported.value = false;
  }
};

const ensureValidSelectedMic = () => {
  // Implementation remains unchanged
};

// Start transcription with waveform
const startTranscription = async (): Promise<void> => {
  canUseContent.value = false;
  validating.value = true;
  if (!selectedMicrophone.value) return;

  try {
    const token = await getSpeechRecognitionToken();
    socket = new WebSocket(
      `wss://api.assemblyai.com/v2/realtime/ws?sample_rate=16000&token=${token}`,
    );

    socket.onerror = (event) => {
      console.error('WebSocket error:', event);
      socket?.close();
      socket = null;
      $q.notify({
        message: 'An error has occurred. Please try again.',
        color: 'red',
        timeout: 3000,
        icon: 'error',
      });
      isRecording.value = false;
      validating.value = false;
    };

    socket.onclose = () => {
      console.log('WebSocket closed');
      socket = null;
      isRecording.value = false;
      validating.value = false;
    };

    let texts: Record<number, string> = {};
    socket.onmessage = async ({ data }) => {
      if (!socket) return;

      const res = JSON.parse(data) as TranscriptionMessage;
      texts[res.audio_start] = res.text;

      const sorted = Object.keys(texts)
        .map(Number)
        .sort((a, b) => a - b)
        .map((key) => texts[key])
        .join(' ');

      // Show partial transcripts in real-time with typing effect
      if (res.message_type === 'PartialTranscript') {
        addToTypingQueue(sorted);
      }

      if (res.message_type === 'FinalTranscript') {
        // Add final transcript to the main transcription
        const finalText = sorted.trim();
        if (finalText) {
          transcriptionText.value =
            `${transcriptionText.value.trim()} ${finalText}`.trim();
          // Clear the live display for next segment
          displayedTranscription.value = transcriptionText.value;
          delete texts[res.audio_start];
        }
      }
    };

    socket.onopen = async () => {
      try {
        let audioConstraints: MediaStreamConstraints = {
          audio: {
            deviceId: selectedMicrophone.value
              ? { exact: selectedMicrophone.value }
              : undefined,
          },
        };
        let currentStream: MediaStream;
        try {
          currentStream = await navigator.mediaDevices.getUserMedia(
            audioConstraints,
          );

          mediaStream.value = currentStream;
        } catch (error: any) {
          if (error.name === 'OverconstrainedError') {
            console.warn(
              'Selected mic not found. Falling back to default mic.',
            );
            audioConstraints.audio = true; // fallback to any available mic
            currentStream = await navigator.mediaDevices.getUserMedia(
              audioConstraints,
            );
          } else {
            throw error;
          }
        }

        recorder = new RecordRTC(currentStream, {
          type: 'audio',
          mimeType: 'audio/webm;codecs=pcm',
          recorderType: StereoAudioRecorder,
          timeSlice: 250,
          desiredSampRate: 16000,
          numberOfAudioChannels: 1,
          bufferSize: 4096,
          audioBitsPerSecond: 128000,
          ondataavailable: async (blob) => {
            const base64data = await readAsDataURL(blob);
            socket?.send(
              JSON.stringify({
                audio_data: base64data.split('base64,')[1],
              }),
            );
          },
        });

        validating.value = false;
        isRecording.value = true;
        showSettings.value = false;
        recorder.startRecording();
      } catch (error) {
        isRecording.value = false;
        validating.value = false;
        console.error('Error setting up recorder:', error);
        socket?.close();
        socket = null;
        $q.notify({
          message: 'An error has occurred. Please try again.',
          color: 'red',
          timeout: 3000,
          icon: 'error',
        });
      }
    };
  } catch (err) {
    isRecording.value = false;
    validating.value = false;
    socket = null;
    console.error('Failed to start transcription:', err);
  }
};

// Cleanup typing effects
const cleanupTyping = () => {
  if (typingTimeout) {
    clearTimeout(typingTimeout);
    typingTimeout = null;
  }
  isTyping.value = false;
  typingQueue = [];
};

// Stop transcription and visualization
const stopTranscription = (): void => {
  recorder?.stopRecording();
  recorder?.destroy();
  recorder = null;

  socket?.send(JSON.stringify({ terminate_session: true }));
  socket?.close();
  socket = null;

  // Clean up typing effects
  cleanupTyping();

  isRecording.value = false;
  if (transcriptionText.value.length > 0) canUseContent.value = true;
  else canUseContent.value = false;
};

const fixGrammarUsingManny = async (): Promise<string> => {
  try {
    loadingFixGrammar.value = true;
    const promptRequest = `Please correct the grammar, punctuation, spelling, and any other language-related issues in the following text: ${transcriptionText.value}.`;
    transcriptionText.value = await composeText(promptRequest);
  } catch (e) {
    console.error(e);
    $q.notify({
      message: 'An error has occurred. Please try again.',
      color: 'red',
      timeout: 3000,
      icon: 'error',
    });
  } finally {
    loadingFixGrammar.value = false;
  }
};

const clearText = async () => {
  const confirmed = await confirmOverrideText($q, {
    title: 'Clear Transcription',
    message: 'Are you sure you want to clear the transcription text?',
  });
  if (!confirmed) return;
  transcriptionText.value = '';
};

const readAsDataURL = (blob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
};

if (!navigator.mediaDevices?.getUserMedia) {
  isBrowserSupported.value = false;
} else {
  requestMicrophoneAccess();
}

navigator.mediaDevices.addEventListener('devicechange', async () => {
  console.log('Device list changed');
  await fetchMicrophones();
  ensureValidSelectedMic();
});

const toggleSettings = () => {
  showSettings.value = !showSettings.value;
};
</script>

<style scoped>
/* Modern Transcription App Styles */
.transcription-app {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  height: 100%;
}

/* Header Styles */
.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 20px;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.robot-avatar {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pulse-ring {
  position: absolute;
  width: 52px;
  height: 52px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

.header-text {
  flex: 1;
}

.title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 2px;
}

.subtitle {
  font-size: 13px;
  opacity: 0.9;
  display: flex;
  align-items: center;
}

.subtitle.success {
  color: #4ade80;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.header-btn {
  color: rgba(255, 255, 255, 0.8);
}

.header-btn:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
}

/* Recording Indicator */
.recording-indicator {
  display: inline-flex;
  gap: 2px;
  margin-right: 8px;
}

.recording-indicator span {
  width: 4px;
  height: 4px;
  background-color: #ef4444;
  border-radius: 50%;
  animation: recording-pulse 1.4s infinite ease-in-out;
}

.recording-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}
.recording-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes recording-pulse {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Typing Dots */
.typing-dots {
  display: inline-flex;
  gap: 2px;
  margin-right: 8px;
}

.typing-dots span {
  width: 4px;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}
.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Settings Panel */
.settings-panel {
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  flex-shrink: 0;
}

.settings-content {
  padding: 16px 20px;
}

.settings-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  color: #475569;
  font-weight: 500;
  font-size: 14px;
}

.settings-select {
  margin-top: 8px;
}

/* Error Section */
.error-section {
  padding: 40px 20px;
  text-align: center;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.error-text {
  max-width: 300px;
}

.error-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.error-message {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
}

/* Recording Section */
.recording-section {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.recording-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 20px 0;
}

.visualization-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60px;
}

.audio-visualizer {
  border-radius: 8px;
  background: rgba(102, 126, 234, 0.1);
  padding: 10px;
}

.ready-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 20px;
  text-align: center;
}

.ready-text {
  font-size: 16px;
  color: #6b7280;
  font-weight: 500;
}

.control-buttons {
  display: flex;
  gap: 16px;
  align-items: center;
}

.record-btn {
  width: 64px;
  height: 64px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

.record-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.toggle-btn {
  width: 48px;
  height: 48px;
}

/* Transcription Container */
.transcription-container {
  margin-top: 16px;
}

.transcription-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px 8px 0 0;
  border: 1px solid #e2e8f0;
  border-bottom: none;
}

.transcription-title {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 600;
  color: #ef4444;
}

.live-dot {
  width: 8px;
  height: 8px;
  background-color: #ef4444;
  border-radius: 50%;
  animation: live-pulse 2s infinite;
}

@keyframes live-pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

.transcription-content {
  border: 1px solid #e2e8f0;
  border-radius: 0 0 8px 8px;
  background: white;
  min-height: 120px;
}

.transcription-content.live {
  background: #fafafa;
}

.transcription-text {
  padding: 16px;
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
  font-size: 15px;
  line-height: 1.6;
  color: #374151;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 200px;
  overflow-y: auto;
}

.modern-cursor {
  display: inline-block;
  width: 2px;
  height: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin-left: 2px;
  animation: modernBlink 1s infinite;
  vertical-align: text-bottom;
}

@keyframes modernBlink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

.waiting-message {
  padding: 40px 16px;
  text-align: center;
  color: #6b7280;
  font-style: italic;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.transcription-input {
  border: none !important;
}

.transcription-input .q-field__control {
  border-radius: 0 0 8px 8px !important;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 0;
  margin-top: 16px;
  border-top: 1px solid #e2e8f0;
}

.action-btn {
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.use-btn {
  padding: 8px 20px;
  border-radius: 8px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.2s ease;
}

.use-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

/* Mobile Responsive */
@media (max-width: 599px) {
  .app-header {
    padding: 12px 16px;
  }

  .header-content {
    gap: 8px;
  }

  .title {
    font-size: 14px;
  }

  .subtitle {
    font-size: 12px;
  }

  .recording-section {
    padding: 16px;
  }

  .recording-controls {
    gap: 16px;
    padding: 16px 0;
  }

  .control-buttons {
    gap: 12px;
  }

  .record-btn {
    width: 56px;
    height: 56px;
  }

  .toggle-btn {
    width: 40px;
    height: 40px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .action-buttons .q-btn {
    width: 100%;
  }

  .use-btn {
    order: -1; /* Primary action first on mobile */
  }
}

/* Scrollbar styling */
.transcription-text::-webkit-scrollbar {
  width: 6px;
}

.transcription-text::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.transcription-text::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.transcription-text::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
