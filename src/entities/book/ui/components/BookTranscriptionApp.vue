<template>
  <q-card
    :style="{
      overflow: 'hidden',
    }"
  >
    <!-- Header -->
    <q-card-section class="q-pa-none">
      <q-bar class="bg-primary text-white">
        <q-icon name="img:robot.png" />
        <div>Real-Time Transcription</div>
        <q-space />

        <!-- Settings Toggle -->
        <q-btn
          dense
          flat
          round
          icon="settings"
          @click="toggleSettings"
          v-if="!isRecording"
        />

        <!-- Close Button -->
        <q-btn dense flat icon="close" @click="emit('close')" v-close-popup>
          <q-tooltip class="bg-white text-primary">Close</q-tooltip>
        </q-btn>
      </q-bar>
    </q-card-section>

    <!-- Settings Panel -->
    <transition name="slide">
      <div
        v-show="showSettings"
        class="settings-panel shadow-2"
        :style="{
          position: 'absolute',
          top: '48px', // Height of q-bar
          right: '0',
          zIndex: 100,
          width: $q.screen.gt.xs ? '280px' : '100%',
        }"
      >
        <div class="full-height q-pa-md">
          <q-select
            v-model="selectedMicrophone"
            :options="microphoneOptions"
            label="Select Microphone"
            option-value="deviceId"
            option-label="label"
            emit-value
            map-options
            outlined
            style="width: 100%"
            :disable="!isBrowserSupported || microphoneOptions.length === 0"
          >
            <template #prepend>
              <q-icon name="mic" />
            </template>
          </q-select>
        </div>
      </div>
    </transition>

    <!-- Main Content -->
    <q-card-section
      class="q-pa-none flex justify-center"
      v-if="!isBrowserSupported"
    >
      <h1 class="self-center text-center text-h6">
        <q-icon name="img:robot.png" size="xl" />
        Sorry, your browser is not supported or no microphone is available.
      </h1>
    </q-card-section>

    <!-- Real-Time Transcription Area -->
    <q-card-section v-else style="padding: 16px; position: relative">
      <!-- Audio Visualization Container -->
      <div
        class="q-pa-md flex items-center justify-evenly q-btn-group"
        ref="container"
      >
        <AVMedia
          v-if="isRecording"
          line-color="#000"
          audio-controls
          :media="isRecording ? mediaStream : null"
          type="line"
          :canv-height="50"
          :canv-width="300"
          canv-fill-color="#00AAFF"
          canv-class="q-mx-md"
        />
        <h3 v-else class="self-center text-center text-h7 q-mx-sm">
          <q-icon name="img:robot.png" size="md" />
          {{
            validating
              ? 'Checking your device. Preparing to begin—please wait.'
              : 'Click the mic button to start transcription.'
          }}
        </h3>
        <!-- Button Group at Bottom -->
        <div>
          <q-btn-group spread rounded>
            <q-btn
              :icon="!isRecording ? 'mic' : 'stop'"
              :color="isRecording ? 'red' : 'primary'"
              @click="isRecording ? stopTranscription() : startTranscription()"
              :disable="validating"
              :loading="validating"
              :class="{ 'bg-red': isRecording }"
            >
              <q-tooltip class="bg-white text-primary">
                {{ isRecording ? 'Stop Recording' : 'Start Recording' }}
              </q-tooltip>
            </q-btn>
            <q-btn
              :disabled="
                !transcriptionText.length || !canUseContent || isRecording
              "
              color="light-blue-8"
              icon="record_voice_over"
              @click="useTranscription"
            >
              <q-tooltip class="bg-white text-primary">
                Use the Transcription Content
              </q-tooltip>
            </q-btn>
            <!-- Toggle Transcription Text -->
            <q-btn
              :disabled="!transcriptionText.length || !isRecording"
              :icon="showTranscription ? 'visibility_off' : 'visibility'"
              @click="showTranscription = !showTranscription"
              color="teal-9"
            >
              <q-tooltip class="bg-white text-primary">
                {{
                  showTranscription
                    ? 'Hide Transcription Text'
                    : 'Show Transcription Text'
                }}
              </q-tooltip>
            </q-btn>
          </q-btn-group>
        </div>
      </div>

      <!-- Typing Effect Area -->
      <transition name="fade">
        <div
          v-show="showTranscription"
          class="q-mt-md row justify-center transcriptionText"
        >
          <div
            v-if="isRecording || !transcriptionText.length"
            class="transcription-typewriter"
            ref="typewriterContainer"
          >
            {{ transcriptionText }} {{ displayText }}
            <span class="cursor-blink">|</span>
          </div>

          <!-- Editable Area -->
          <q-input
            v-if="!isRecording && transcriptionText.length"
            v-model="transcriptionText"
            ref="typewriterContainer"
            type="textarea"
            placeholder="Transcription will appear here..."
            outlined
            autogrow
            style="width: 100%; height: 400px !important; overflow: auto"
            :input-style="{ minHeight: '120px' }"
          >
            <template #append>
              <q-btn-group
                spread
                flat
                style="flex-direction: column; margin-top: auto; height: 120px"
                class="flex items-center justify-center"
              >
                <q-btn
                  dense
                  flat
                  icon="backspace"
                  :disable="!transcriptionText.length"
                  @click="clearText"
                >
                  <q-tooltip>Clear Content</q-tooltip>
                </q-btn>
                <q-btn
                  :disabled="
                    !transcriptionText.length || !canUseContent || isRecording
                  "
                  dense
                  flat
                  icon="record_voice_over"
                  @click="useTranscription"
                >
                  <q-tooltip class="bg-white text-primary">
                    Use the Transcription Content
                  </q-tooltip>
                </q-btn>
                <q-btn
                  dense
                  flat
                  icon="img:robot.png"
                  :loading="loadingFixGrammar"
                  :disable="loadingFixGrammar || !transcriptionText.length"
                  @click="fixGrammarUsingManny"
                >
                  <q-tooltip>Ask Manny to fix the grammar</q-tooltip>
                </q-btn>
              </q-btn-group>
            </template>
          </q-input>
        </div>
      </transition>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { ref, watch, onUnmounted, onBeforeUnmount, nextTick } from 'vue';
import RecordRTC, { StereoAudioRecorder } from 'recordrtc';
import { getSpeechRecognitionToken } from 'src/shared/api/speech';
import { composeText } from 'src/shared/api/openai';
import { useQuasar } from 'quasar';
import { AVMedia } from 'vue-audio-visual';
import { confirmOverrideText } from 'src/shared/lib/quasar-dialogs';

interface MediaDevice {
  deviceId: string;
  label: string;
}
interface MessageBase {
  audio_start: number;
  text: string;
  message_type: string;
}
interface MessagePartial extends MessageBase {
  message_type: 'PartialTranscript';
}
interface MessageFinal extends MessageBase {
  message_type: 'FinalTranscript';
  punctuated: boolean;
  text_formatted: boolean;
}
type TranscriptionMessage = MessagePartial | MessageFinal;

const $q = useQuasar();
const validating = ref<boolean>(false);
const selectedMicrophone = ref<string | null>(null);
const microphoneOptions = ref<MediaDevice[]>([]);
const isBrowserSupported = ref<boolean>(true);
const isRecording = ref<boolean>(false);
const canUseContent = ref<boolean>(false);
const loadingFixGrammar = ref<boolean>(false);
const transcriptionText = ref<string>('');
let socket: WebSocket | null = null;
let recorder: RecordRTC | null = null;
const showSettings = ref(false);
const audioBlobUrl = ref('');
const audioElement = ref<HTMLAudioElement | null>(null);
const displayText = ref('');
const fullText = ref('');
const typingIndex = ref(0);
const typingSpeed = 20; // milliseconds per character
const mediaStream = ref<MediaStream | null>(null);
const showTranscription = ref<boolean>(false);

const emit = defineEmits<{
  completed: [content: string];
  close?: [];
}>();

const typeCharacter = async () => {
  while (typingIndex.value < fullText.value.length) {
    displayText.value += fullText.value[typingIndex.value];
    typingIndex.value++;
    await new Promise((resolve) => setTimeout(resolve, typingSpeed));
  }
};

const canvasWidth = ref<number>(500);
const container = ref<HTMLElement | null>(null);
const typewriterContainer = ref<HTMLElement | null>(null);

// Cleanup
onUnmounted(async () => {
  await nextTick();
  updateCanvasWidth();
  scrollToBottom();
  if (audioBlobUrl.value) URL.revokeObjectURL(audioBlobUrl.value);
  if (audioElement.value) audioElement.value = null;

  window.addEventListener('resize', updateCanvasWidth);
});

const scrollToBottom = () => {
  if (typewriterContainer.value) {
    // Smooth scroll (optional)
    typewriterContainer.value.scrollTop =
      typewriterContainer.value.scrollHeight;
  }
};
const updateCanvasWidth = () => {
  if (container.value) {
    canvasWidth.value = container.value.offsetWidth;
  }
};

// Optional: Watch for recording state change
watch(
  () => isRecording.value,
  (newVal) => {
    if (newVal) {
      // Wait for DOM to update before accessing container
      setTimeout(() => {
        updateCanvasWidth();
      }, 0);
    }
  },
);
watch(
  () => transcriptionText.value,
  () => {
    // Use nextTick to wait for DOM update
    setTimeout(() => {
      scrollToBottom();
    }, 0);
  },
);

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateCanvasWidth);
});
// Existing functions remain unchanged
const useTranscription = () => {
  emit('completed', { content: transcriptionText.value });
};

const requestMicrophoneAccess = async (): Promise<void> => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    stream.getTracks().forEach((track) => track.stop());
    await fetchMicrophones();
  } catch (error) {
    console.warn('Microphone permission denied or not available.', error);
    isBrowserSupported.value = false;
    microphoneOptions.value = [];
  }
};

const fetchMicrophones = async (): Promise<void> => {
  try {
    const devices = await navigator.mediaDevices.enumerateDevices();
    const audioInputDevices = devices.filter(
      (device) => device.kind === 'audioinput',
    );
    microphoneOptions.value = audioInputDevices.map((device, index) => ({
      deviceId: device.deviceId,
      label: device.label || `Microphone ${index + 1}`,
    }));
    if (microphoneOptions.value.length > 0 && !selectedMicrophone.value) {
      selectedMicrophone.value = microphoneOptions.value[0].deviceId;
    }
    isBrowserSupported.value = true;
  } catch (error) {
    console.error('Error enumerating devices:', error);
    isBrowserSupported.value = false;
  }
};

const ensureValidSelectedMic = () => {
  // Implementation remains unchanged
};

// Start transcription with waveform
const startTranscription = async (): Promise<void> => {
  canUseContent.value = false;
  validating.value = true;
  if (!selectedMicrophone.value) return;

  try {
    const token = await getSpeechRecognitionToken();
    socket = new WebSocket(
      `wss://api.assemblyai.com/v2/realtime/ws?sample_rate=16000&token=${token}`,
    );

    socket.onerror = (event) => {
      console.error('WebSocket error:', event);
      socket?.close();
      socket = null;
      $q.notify({
        message: 'An error has occurred. Please try again.',
        color: 'red',
        timeout: 3000,
        icon: 'error',
      });
      isRecording.value = false;
      validating.value = false;
    };

    socket.onclose = () => {
      console.log('WebSocket closed');
      socket = null;
      isRecording.value = false;
      validating.value = false;
    };

    let texts: Record<number, string> = {};
    socket.onmessage = async ({ data }) => {
      if (!socket) return;

      const res = JSON.parse(data) as TranscriptionMessage;
      texts[res.audio_start] = res.text;

      const sorted = Object.keys(texts)
        .map(Number)
        .sort((a, b) => a - b)
        .map((key) => texts[key])
        .join(' ');
      console.log('Recording...');

      if (res.message_type === 'FinalTranscript') {
        fullText.value = sorted;
        typingIndex.value = 0;
        await typeCharacter();
        displayText.value = '';
        transcriptionText.value = `${transcriptionText.value.trim()} ${sorted.trim()}`;
        delete texts[res.audio_start];
      }
    };

    socket.onopen = async () => {
      try {
        let audioConstraints: MediaStreamConstraints = {
          audio: {
            deviceId: selectedMicrophone.value
              ? { exact: selectedMicrophone.value }
              : undefined,
          },
        };
        let currentStream: MediaStream;
        try {
          currentStream = await navigator.mediaDevices.getUserMedia(
            audioConstraints,
          );

          mediaStream.value = currentStream;
        } catch (error: any) {
          if (error.name === 'OverconstrainedError') {
            console.warn(
              'Selected mic not found. Falling back to default mic.',
            );
            audioConstraints.audio = true; // fallback to any available mic
            currentStream = await navigator.mediaDevices.getUserMedia(
              audioConstraints,
            );
          } else {
            throw error;
          }
        }

        recorder = new RecordRTC(currentStream, {
          type: 'audio',
          mimeType: 'audio/webm;codecs=pcm',
          recorderType: StereoAudioRecorder,
          timeSlice: 250,
          desiredSampRate: 16000,
          numberOfAudioChannels: 1,
          bufferSize: 4096,
          audioBitsPerSecond: 128000,
          ondataavailable: async (blob) => {
            const base64data = await readAsDataURL(blob);
            socket?.send(
              JSON.stringify({
                audio_data: base64data.split('base64,')[1],
              }),
            );
          },
        });

        validating.value = false;
        isRecording.value = true;
        showSettings.value = false;
        recorder.startRecording();
      } catch (error) {
        isRecording.value = false;
        validating.value = false;
        console.error('Error setting up recorder:', error);
        socket?.close();
        socket = null;
        $q.notify({
          message: 'An error has occurred. Please try again.',
          color: 'red',
          timeout: 3000,
          icon: 'error',
        });
      }
    };
  } catch (err) {
    isRecording.value = false;
    validating.value = false;
    socket = null;
    console.error('Failed to start transcription:', err);
  }
};

// Stop transcription and visualization
const stopTranscription = (): void => {
  recorder?.stopRecording();
  recorder?.destroy();
  recorder = null;

  socket?.send(JSON.stringify({ terminate_session: true }));
  socket?.close();
  socket = null;

  isRecording.value = false;
  if (transcriptionText.value.length > 0) canUseContent.value = true;
  else canUseContent.value = false;
};

const fixGrammarUsingManny = async (): Promise<string> => {
  try {
    loadingFixGrammar.value = true;
    const promptRequest = `Please correct the grammar, punctuation, spelling, and any other language-related issues in the following text: ${transcriptionText.value}.`;
    transcriptionText.value = await composeText(promptRequest);
  } catch (e) {
    console.error(e);
    $q.notify({
      message: 'An error has occurred. Please try again.',
      color: 'red',
      timeout: 3000,
      icon: 'error',
    });
  } finally {
    loadingFixGrammar.value = false;
  }
};

const clearText = async () => {
  const confirmed = await confirmOverrideText($q, {
    title: 'Clear Transcription',
    message: 'Are you sure you want to clear the transcription text?',
  });
  if (!confirmed) return;
  transcriptionText.value = '';
};

const readAsDataURL = (blob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
};

if (!navigator.mediaDevices?.getUserMedia) {
  isBrowserSupported.value = false;
} else {
  requestMicrophoneAccess();
}

navigator.mediaDevices.addEventListener('devicechange', async () => {
  console.log('Device list changed');
  await fetchMicrophones();
  ensureValidSelectedMic();
});

const toggleSettings = () => {
  showSettings.value = !showSettings.value;
};
</script>

<style scoped>
.settings-panel {
  background: #f9f9f9;
}

/* Absolute position utilities */
.absolute-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
  transform: translateX(0);
  opacity: 1;
}

.slide-enter-from,
.slide-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.audio-wave-canvas {
  width: 100%;
  height: 150px;
}

@keyframes blink-caret {
  from,
  to {
    border-color: transparent;
  }
  50% {
    border-color: #000;
  }
}

.transcription-typewriter {
  padding: 16px;
  border: 1px solid #ccc;
  border-radius: 4px;
  width: 100%;
  font-size: 0.8rem;
  line-height: 1.5;
  white-space: pre-line;
  height: 400px;
  overflow: auto;
}

.cursor-blink {
  display: inline-block;
  width: 2px;
  animation: blink-caret 1s infinite;
}
</style>
