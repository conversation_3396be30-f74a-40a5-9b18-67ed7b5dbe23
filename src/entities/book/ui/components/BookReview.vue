<template>
  <div id="review" class="container">
    <div class="row" v-if="comments.length == 0 && !isInReview">
      <q-card flat bordered class="full-width rounded-borders">
        <q-card-section class="q-pa-none">
          <q-bar class="bg-primary text-white" style="font-size: 0.8rem">
            Contact {{ reviewer }} for book review.
          </q-bar>
        </q-card-section>
        <q-separator />
        <q-card-section>
          <q-form
            id="contact-form"
            class="q-gutter-md"
            @submit.prevent="onSubmit"
          >
            <div id="title" class="q-mb-lg"></div>
            <q-input
              v-model="message"
              label="Message"
              type="textarea"
              required
              outlined
              autogrow
              input-style="min-height: 200px"
            />
          </q-form>
        </q-card-section>
        <q-separator />
        <q-card-section class="q-pa-none bg-primary">
          <q-card-actions align="right">
            <q-btn
              flat
              icon="send"
              label="Submit"
              color="white"
              type="submit"
              @click="onSubmit"
              :loading="isSending"
              :disable="isSending || !message"
          /></q-card-actions>
        </q-card-section>
      </q-card>
    </div>
    <div
      class="row items-center q-mb-lg"
      v-if="comments.length > 0 || isInReview"
    >
      <div class="col-6">
        <q-select
          v-model="filter"
          label="Comment Filter"
          :options="filterOptions"
          class="q-mr-md"
          @change="slide = 0"
          dense
        >
          <template v-slot:before>
            <q-avatar color="primary" text-color="white">
              {{ filteredComments.length }}
            </q-avatar>
          </template>
        </q-select>
      </div>
      <div class="col-6">
        <q-select
          v-model="filterUsers"
          label="Created by"
          :options="filterByUsers"
          @change="slide = 0"
          dense
        />
      </div>
    </div>

    <q-carousel
      v-model="slide"
      ref="carouselReview"
      swipeable
      infinite
      transition-prev="slide-right"
      transition-next="slide-left"
      animated
      padding
      height="calc(100vh - 300px)"
      style="overflow: hidden"
      v-if="filteredComments.length"
    >
      <q-carousel-slide
        v-for="(comment, index) in filteredComments"
        :key="index"
        :name="index"
        class="slide-review q-pa-none"
      >
        <div class="q-pa-none">
          <q-card flat bordered separator class="drawer-list rounded-borders">
            <q-card-section class="q-pa-none">
              <q-bar
                class="text-white q-pa-xs"
                :class="
                  comment.isResolved === true ? 'bg-primary ' : 'bg-danger'
                "
              >
                <q-avatar
                  :icon="comment.isResolved === true ? 'check' : 'close'"
                  text-color="white"
                  :color="comment.isResolved === true ? 'success' : 'danger'"
                  size="sm"
                  v-if="userId == book.authorId"
                >
                  <q-tooltip>
                    {{
                      comment.isResolved === true
                        ? 'Status: Resolved'
                        : 'Status: UnResolved'
                    }}</q-tooltip
                  >
                </q-avatar>
                <q-btn
                  dense
                  round
                  flat
                  icon="arrow_back"
                  @click="$refs.carouselReview.previous()"
                />
                <q-btn
                  dense
                  round
                  flat
                  icon="arrow_forward"
                  @click="$refs.carouselReview.next()"
                />
                <q-item>
                  <q-item-section>
                    <q-item-label lines="1" style="font-size: 0.8rem">
                      {{
                        trimText(
                          `${formatDate(comment.createdAt)} by ${
                            comment.user?.name || comment.user?.email || ''
                          }`,
                          15,
                        )
                      }}
                      <q-tooltip>
                        {{
                          comment.isResolved === true
                            ? 'Status: Resolved'
                            : 'Status: UnResolved'
                        }}
                        <br />
                        {{ formatDate(comment.createdAt) }} by
                        {{ comment.user?.name || comment.user?.email || '' }}
                      </q-tooltip>
                    </q-item-label>
                  </q-item-section>
                </q-item>
                <q-space />

                <q-btn flat round dense icon="more_vert">
                  <q-menu>
                    <q-list style="min-width: 100px">
                      <q-item
                        clickable
                        v-close-popup
                        @click="selectComment(comment)"
                      >
                        <q-item-section>Find</q-item-section>
                      </q-item>
                      <q-item
                        clickable
                        v-close-popup
                        @click="markAsResolved(comment, true)"
                        v-if="isReviewMode"
                      >
                        <q-item-section
                          >Mark as
                          {{
                            comment.isResolved ? 'Un-resolve' : 'Resolve'
                          }}</q-item-section
                        >
                      </q-item>
                      <q-item
                        clickable
                        v-close-popup
                        v-if="comment.revhistory && comment.revhistory?.length"
                        @click="
                          selectComment(comment);
                          revisionDialog = true;
                        "
                      >
                        <q-item-section>Revision History</q-item-section>
                      </q-item>
                      <q-item
                        clickable
                        v-close-popup
                        @click="deleteComment(comment)"
                        v-if="comment.userId == userId || isInReview"
                      >
                        <q-item-section>Delete</q-item-section>
                      </q-item>
                    </q-list>
                  </q-menu>
                </q-btn>
              </q-bar>
            </q-card-section>

            <q-card-section class="q-pa-none">
              <q-item class="q-pa-none">
                <q-item-section
                  :clickable="!comment.isResolved"
                  v-if="comment.selection"
                  style="cursor: pointer"
                  class="q-py-none"
                >
                  <q-item>
                    <q-item-section avatar class="items-center">
                      <q-item-label style="font-size: 0.6rem">{{
                        userId == book.authorId
                          ? 'Click to Save'
                          : comment.isResolved
                          ? 'Mark as Un-resolved'
                          : 'Mark as Resolved'
                      }}</q-item-label>
                      <q-checkbox
                        :checked-icon="
                          userId != book.authorId ? 'check' : 'save'
                        "
                        :unchecked-icon="
                          userId != book.authorId ? 'close' : 'save'
                        "
                        class="resolve-btn"
                        :class="
                          userId == book.authorId
                            ? 'bg-primary q-btn--round'
                            : comment.isResolved
                            ? 'bg-success q-btn--round'
                            : 'bg-danger q-btn--round'
                        "
                        v-model="comment.isResolved"
                        color="white"
                        @update:model-value="markAsResolved(comment, false)"
                      >
                        <q-tooltip
                          anchor="top middle"
                          self="bottom middle"
                          :offset="[0, 4]"
                          v-if="!comment.isResolved"
                        >
                          {{
                            userId == book.authorId
                              ? 'Click here to save new update'
                              : ' Mark as resolved'
                          }}
                        </q-tooltip>
                        <q-tooltip
                          anchor="top middle"
                          self="bottom middle"
                          :offset="[0, 4]"
                          v-if="comment.isResolved"
                        >
                          {{
                            userId == book.authorId
                              ? 'Click here to Save'
                              : ' Mark as unresolved'
                          }}
                        </q-tooltip>
                      </q-checkbox>
                    </q-item-section>
                    <q-item-section>
                      <q-item-label
                        clickable
                        @click="selectComment(comment)"
                        caption
                        class="multi-line-truncate"
                        lines="3"
                      >
                        {{ comment.selection }}
                      </q-item-label>
                    </q-item-section>
                  </q-item>
                </q-item-section>
              </q-item>
            </q-card-section>
            <q-separator />
            <q-card-section
              :clickable="!comment.isResolved"
              v-if="comment.userId != userId && comment.selection"
              style="cursor: pointer"
              @click="selectComment(comment)"
            >
              <q-item>
                <q-item-section>
                  <q-item-label>Review Feedback</q-item-label>
                  <q-item-label caption>{{ comment.content }}</q-item-label>
                </q-item-section>

                <q-item-section side top>
                  <q-item-label caption>{{
                    formatDate(comment.createdAt)
                  }}</q-item-label>
                  <q-icon name="star" color="yellow" />
                </q-item-section>
              </q-item>
            </q-card-section>

            <!--- review feedback -->
            <q-card-section v-else>
              <q-item-section>
                <q-input
                  v-model="comment.content"
                  :label="
                    comment.userId != userId
                      ? 'Review Feedback'
                      : 'Your review here..'
                  "
                  autogrow
                  outlined
                  dense
                  debounce="500"
                  :ref="'content-' + comment.id"
                  :disable="comment.userId != userId"
                  :readonly="comment.userId != userId"
                  type="textarea"
                  :input-style="{ minHeight: '50px' }"
                >
                  <template v-slot:append>
                    <q-btn
                      flat
                      round
                      icon="email"
                      color="primary"
                      v-if="comment.userId == userId && comment.content"
                      @click="onSubmit(comment.content, comment.selection)"
                      :loading="isSending"
                    >
                      <q-tooltip> Email this review to the author. </q-tooltip>
                    </q-btn>
                  </template>
                </q-input>
              </q-item-section>
            </q-card-section>

            <!--- chat -->
            <q-scroll-area
              ref="scrollChatRef"
              style="height: calc(50vh - 130px); max-width: 500px"
              :thumb-style="thumbStyle"
              :bar-style="barStyle"
            >
              <q-card-section ref="contentChatRef">
                <q-chat-message
                  v-if="comment.replies.length"
                  :stamp="formatDate(reply.createdAt)"
                  :text="[reply.content]"
                  :sent="reply.userId == userId"
                  text-color="white"
                  :bg-color="
                    reply.userId == userId ? 'primary' : 'deep-purple-6'
                  "
                  v-for="(reply, indexTyping) in comment.replies"
                  :key="indexTyping"
                >
                  <template v-slot:name>
                    <q-badge
                      rounded
                      :color="whosOnline[reply.userId] ? 'yellow' : 'grey'"
                      class="q-ml-xs"
                    />
                    {{ reply.user?.name || reply.user?.email || 'Anonymous' }}
                  </template>
                  <template v-slot:avatar>
                    <q-avatar
                      :color="
                        reply.userId == userId ? 'primary' : 'deep-purple-6'
                      "
                      text-color="white"
                    >
                      {{
                        reply.user?.name?.charAt(0) ||
                        reply.user?.email?.charAt(0) ||
                        'Anonymous'
                      }}
                    </q-avatar>
                  </template>
                </q-chat-message>

                <template v-for="(typing, indexTyping) in comment.isTypings">
                  <q-chat-message
                    v-if="notifyUserContentIsReviewing(typing, index, comment)"
                    :key="indexTyping"
                    text-color="white"
                    bg-color="deep-purple-6"
                  >
                    <template v-slot:name>
                      <q-badge rounded color="yellow" class="q-ml-xs" />
                      {{ typing.isTypingByName }}
                    </template>
                    <template v-slot:avatar>
                      <q-avatar color="deep-purple-6" text-color="white">
                        {{ typing.isTypingByName[0] }}
                      </q-avatar>
                    </template>
                    <q-spinner-dots size="2rem" />
                  </q-chat-message>
                </template>
              </q-card-section>
            </q-scroll-area>

            <!--- reply -->
            <q-card-section>
              <q-input
                autogrow
                outlined
                dense
                placeholder="Reply..."
                bg-color="white"
                v-model="reply"
                @keydown.enter.prevent="handleChatEnterPress"
                @update:model-value="addReply(comment.id, true)"
              >
                <template v-slot:append>
                  <q-btn
                    round
                    dense
                    flat
                    :disable="!reply.length"
                    color="primary"
                    icon="send"
                    @click.stop="addReply(comment.id, false)"
                  />
                </template>
              </q-input>
            </q-card-section>
          </q-card>
        </div>
      </q-carousel-slide>
    </q-carousel>
  </div>

  <q-dialog
    v-model="revisionDialog"
    persistent
    transition-show="scale"
    transition-hide="scale"
    :maximized="maximizedToggle"
  >
    <q-card style="min-width: 60vw">
      <q-card-section class="q-pa-none">
        <q-bar class="bg-primary text-white">
          <q-icon name="img:robot.png" />

          <div>Revision History</div>

          <q-space />

          <q-btn
            dense
            flat
            :icon="!maximizedToggle ? 'fullscreen' : 'fullscreen_exit'"
            @click="maximizedToggle = !maximizedToggle"
          >
            <q-tooltip v-if="!maximizedToggle" class="bg-white text-primary"
              >Maximize</q-tooltip
            >
          </q-btn>
          <q-btn dense flat icon="close" v-close-popup>
            <q-tooltip class="bg-white text-primary">Close</q-tooltip>
          </q-btn>
        </q-bar>
      </q-card-section>

      <q-card-section class="q-py-none">
        <div class="q-mt-md">
          <div class="text-h6">Current Content</div>
          <div v-html="selectedComment.selection" class="q-py-md" />
          <q-separator />
        </div>
        <div class="splitter-container">
          <div class="text-h6 sticky-header">Revision History</div>
          <q-separator />
          <q-scroll-area
            :thumb-style="thumbStyle"
            :bar-style="barStyle"
            style="height: calc(100vh - 450px)"
          >
            <q-list bordered class="rounded-borders">
              <div
                v-for="(commentHistory, index) in selectedComment.revhistory"
              >
                <q-item clickable v-ripple>
                  <q-item-section avatar>
                    <q-avatar color="primary" text-color="white">
                      {{ commentHistory.resolvedBy[0] }}
                    </q-avatar>
                  </q-item-section>

                  <q-item-section>
                    <q-item-label>
                      {{ formatDate(commentHistory.resolvedAt) }} by
                      {{ commentHistory.resolvedBy }}
                      <q-tooltip>
                        {{ formatDate(commentHistory.resolvedAt) }} by
                        {{ commentHistory.resolvedBy }}
                      </q-tooltip>
                    </q-item-label>
                    <q-item-label
                      caption
                      v-html="commentHistory.resolveSelection"
                    />
                  </q-item-section>
                  <q-item-section
                    v-if="commentHistory.default === true"
                    side
                    top
                  >
                    Default Content
                    <q-icon name="star" color="yellow" />
                  </q-item-section>
                </q-item>

                <q-separator inset="item" />
              </div>
            </q-list>
          </q-scroll-area>
        </div>
      </q-card-section>
      <q-card-actions
        align="right"
        :class="maximizedToggle ? 'absolute-bottom' : ''"
      >
        <q-btn flat label="Close" color="danger" v-close-popup />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { Book, ChapterOutline } from 'src/entities/book';
import { useVModels } from '@vueuse/core/index';
import { computed, nextTick, ref, watch } from 'vue';
import { useQuasar } from 'quasar';
import { Author, isReviewMode, user, userLoggedIn } from 'src/entities/user';
import { confirmOverrideText, showError } from 'src/shared/lib/quasar-dialogs';
import {
  barStyle,
  thumbStyle,
  trimText,
  sendEmail,
  formatDate,
  getHumanReadableDate,
} from 'src/entities/setting';
import { useNotificationStore } from 'src/entities/notification';
import type TinyMCE from 'tinymce';

declare const tinymce: typeof TinyMCE;

const props = defineProps<{
  book: Book;
  slideReview: Number;
  reviewer: String;
  author: Author;
  selectedChapterId: string;
  isLoadingChapter: boolean;
  readonly?: boolean;
  selectedChapterOutline: ChapterOutline;
}>();

const emits = defineEmits<{
  'update:isLoadingChapter': [isLoadingChapter: boolean];
  'update:book': [book: Book];
  'update:slideReview';
}>();

const {
  book,
  isLoadingChapter,
  selectedChapterId,
  selectedChapterOutline,
  slideReview,
  author,
} = useVModels(props, emits);
// Stores
const notificationStore = useNotificationStore();

// State
const filter = ref('All');
const filterOptions = ref(['All', 'Unresolved', 'Resolved']);
const filterUsers = ref('All');
const filterByUsers = ref(['All', 'You', 'Others']);
const reply = ref('');
const isSending = ref(false);
const message = ref('');
const revisionDialog = ref(false);
const maximizedToggle = ref(false);
const $q = useQuasar();
const scrollChatRef = ref(null);
const contentChatRef = ref(null);

// Other Variables
const reviewer = props.reviewer;
const isInReview = computed(() => isReviewMode.value.length > 0);
const slide = ref(0);
const whosOnline = ref({});
const selectedComment = ref({});

book.value.comments = book.value.comments.map((comment) => ({
  ...comment,
  isTypings: {},
}));

const comments = computed(() => book.value.comments);

// Computed Properties
const userId = ref(user?.value?.uid);
const selectedText = ref('');

const filteredComments = computed(() => {
  return comments.value.filter((comment) => {
    return (
      meetsFilterReqs(comment) &&
      meetsFilterByReqs(comment) &&
      meetsFilterUsersReq(comment)
    );
  });
});

// Methods
const notifyUserContentIsReviewing = (typing, index, comment) => {
  const isUserTyping = typing.isTypingBy != user.value.uid && typing.isTyping;
  whosOnline.value[typing.isTypingBy] = typing.isTypingBy;
  return isUserTyping;
};

const unreadComments = computed(() => {
  if (!user.value) return [];
  const userId = user.value.id;
  const unreadComments = [];
  comments.value.forEach((comment) => {
    let shouldPushComment = false;
    if (!comment.readBy.includes(userId)) shouldPushComment = true;
    comment.replies.forEach((reply) => {
      if (!reply.readBy.includes(userId)) shouldPushComment = true;
    });
    if (shouldPushComment) unreadComments.push(comment);
  });
  return unreadComments;
});
// Methods
const selectComment = async (comment) => {
  selectedComment.value = comment;
  if (comment.id != selectedComment.value.id) {
    reply.value = '';
    // Assuming you have access to a store or a similar state management solution
    // this.$store.commit('app/setSelectedComment', comment);
  }

  if (selectedChapterId.value !== comment.chapterId) {
    selectedChapterId.value = comment.chapterId;
    // Assuming selectChapter is another method in your component
    // selectChapter(comment.chapterId);
  }

  try {
    if (typeof tinymce !== 'undefined') {
      const editor = tinymce.get('editor');

      setTimeout(() => {
        editor.selection.moveToBookmark(comment.selectedBookmark);
        const node = editor.selection.getStart();
        node.scrollIntoView();
      }, 500);
    }
  } catch (err) {
    if (comment?.selection) {
      if (typeof tinymce !== 'undefined') {
        const editor = tinymce.get('editor');
        tinymce.setActive(editor);
        tinymce.activeEditor.execCommand('SearchReplace');
      }
      await navigator.clipboard.writeText(
        decodeHtmlEntities(strippedContent(comment.selection)),
      );
      $q.notify(
        'Review content could not be found! The content has been copied to the clipboard. Please paste it and use the search feature.',
      );
    }
  }
};

const onSubmit = async (messageStr = '', selection = '') => {
  const reviewerEmail = !reviewer
    ? [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ]
    : [reviewer];
  const reviewerName = !reviewer ? 'a reviewer' : reviewer;
  let subject =
    '[Manuscriptr] New book review request from ' + user?.value?.email;

  if (messageStr.length) {
    const selectedChapter = selectedChapterOutline.value;

    const shouldSendEmail = await confirmOverrideText($q, {
      title: 'Send Email?',
      message: `Are you sure you want to send the review to ${reviewerName} via email?`,
    });

    if (!shouldSendEmail) {
      return;
    }

    subject =
      '[Manuscriptr] New book review comment from ' + user?.value?.email;
    message.value = `<p><strong>Review:</strong></p>
                    <p>${messageStr}</p>
                    <p><strong>Content:</strong></p> <p>${selection}</p>
                    <p><strong>Book:</strong> ${book.value.title}</p>
                    <p><strong>Chapter:</strong> ${selectedChapter.title}</p>
                    <p><strong>Reviewer:</strong> ${
                      user?.value?.displayName || user?.value?.email
                    }</p>
                    <p><strong>Date Commented:</strong> ${getHumanReadableDate(
                      Date.now(),
                    )}</p>`;
  } else {
    const shouldSendEmail = await confirmOverrideText($q, {
      title: 'Send Email?',
      message: `Are you sure you want to submit your book for review to ${reviewerName}?`,
    });

    if (!shouldSendEmail) {
      return;
    }

    message.value = `<p><strong>Message:</strong></p>
                    <p>${message.value}</p>
                    <p><strong>Book:</strong> ${book.value.title}</p>
                    <p><strong>Author:</strong> ${
                      user.value.displayName || user.value.email
                    }</p>
                    <p><strong>Requested Date:</strong> ${getHumanReadableDate(
                      Date.now(),
                    )}</p>`;
  }

  if (!message.value) return;
  const msg = `${message.value} <p><strong>Message from:</strong> ${
    user.value.displayName || user.value.email
  }</p>`;

  sendEmailToUser(subject, msg, reviewerEmail);
};

const meetsFilterReqs = (comment) => {
  return (
    filter.value == 'All' ||
    (filter.value == 'Unresolved' && !comment.isResolved) ||
    (filter.value == 'Resolved' && comment.isResolved) ||
    (filter.value == 'Unread' && hasUnreadComment(comment)) ||
    (filter.value == 'Read' && !hasUnreadComment(comment))
  );
};

const meetsFilterUsersReq = (comment) => {
  return (
    filterUsers.value == 'All' ||
    (filterUsers.value == 'You' && comment.userId == user.value.uid) ||
    (filterUsers.value == 'Others' && comment.userId != user.value.uid)
  );
};

const meetsFilterByReqs = (comment) => {
  return comment.chapterId == selectedChapterId.value;
};

const hasUnreadComment = (comment) => {
  return !!unreadComments.value.find(
    (unreadComment) => unreadComment.id === comment.id,
  );
};

const markAsResolved = async (comment, fromClick = false) => {
  const id = comment.id;
  const index = comments.value.findIndex((c) => c.id === id);

  if (index !== -1) {
    if (user.value?.uid === book.value.authorId) {
      const bookMarkNew = getSelectedText();
      if (!selectedText.value) {
        showError(
          $q,
          'To proceed, please highlight or select the content you want to modify in the editor. This will serve as the updated content for this review comment.',
        );
        return;
      }
      const currSel = comments.value[index].selection;
      if (currSel === selectedText.value) {
        showError(
          $q,
          'To proceed, please update or revised the highlighted content.',
        );
        return;
      }
      const confirmed = await confirmOverrideText($q, {
        title: `Save New Revision?`,
        message: `Are you sure you want to update the content for this review?`,
      });

      if (!confirmed) {
        return;
      }

      if (!comments.value[index].revhistory) {
        comments.value[index].revhistory = [
          {
            default: true,
            resolvedAt: comments.value[index].createdAt,
            readBy:
              comments.value[index].user.name ||
              comments.value[index].user.email,
            resolveSelection: comments.value[index].selection,
            resolvedBy: comments.value[index].resolvedBy,
            content: comments.value[index].content,
          },
        ];
      }
      comments.value[index].revhistory.push({
        default: false,
        resolvedAt: Date.now(),
        readBy: user.value?.displayName || user.value?.email,
        resolveSelection: selectedText.value,
        resolvedBy: user.value?.displayName || user.value?.email,
        content: comments.value[index].content,
      });
      comments.value[index].selection = selectedText.value;
      comments.value[index].selectedBookmark = bookMarkNew;
      comments.value[index].isResolved = false;
      comments.value[index].resolvedAt = '';
      comments.value[index].resolvedBy = '';
      comments.value[index].readBy = '';

      $q.notify({
        message: 'Update successfully saved.',
        color: 'primary',
        timeout: 3000,
        icon: 'check',
      });

      const subject = `[Manuscriptr] Book Review Updated  by
      ${user.value?.displayName || user.value?.email}`;
      const message = `<p>Dear ${
        comments.value[index].user.name || comments.value[index].user.email
      },</p>
          <p>The feedback for the book chapter review has been updated. Below are the details for your reference:</p>
                    <p><strong>Review:</strong></p><p> ${
                      comments.value[index].content
                    }
                    </p>
                    <p><strong>Content:</strong></p><p> ${currSel}</p>
                    <p><strong>Revised Content:</strong></p><p> ${
                      selectedText.value
                    }</p>
                    <p><strong>Book:</strong> ${book.value.title}</p>
                    <p><strong>Chapter:</strong> ${
                      selectedChapterOutline.value.title
                    }</p>
                    <p><strong>Updated By:</strong> ${
                      user.value?.displayName || user.value?.email
                    }</p>
                    <p><strong>Updated Date:</strong> ${getHumanReadableDate(
                      Date.now(),
                    )}</p>
          <p> Please feel free to review the updated review comment.</p>
          <p>The Manuscriptr Team</p>`;

      if (!userLoggedIn.value?.assignedAdminEmail) return;

      sendEmailToUser(subject, message, userLoggedIn.value?.assignedAdminEmail);
      return;
    }
    const confirmed = await confirmOverrideText($q, {
      title: `Mark as ${
        comments.value[index].resolvedAt ? 'Un-Resolved' : 'Resolved'
      }`,
      message: `Are you sure you want to ${
        comments.value[index].resolvedAt ? 'Un-resolve' : 'resolve'
      } this review?`,
    });

    if (!confirmed) {
      if (fromClick === false)
        comments.value[index].isResolved = !comments.value[index].isResolved;
      return;
    }
    let resStatus;
    if (comments.value[index].resolvedAt) {
      comments.value[index].isResolved = false;
      resStatus = 'Un-Resolved';
      comments.value[index].resolvedAt = '';
      comments.value[index].resolvedBy = '';
      comments.value[index].readBy = '';
    } else {
      comments.value[index].isResolved = true;
      resStatus = 'Resolved';
      comments.value[index].resolvedAt = Date.now();
      comments.value[index].resolvedBy =
        user.value?.displayName || user.value?.email;
      comments.value[index].readBy =
        user.value?.displayName || user.value?.email;
    }

    const subject = `[Manuscriptr] Book Review Feedback ${resStatus} by
      ${user.value?.displayName || user.value?.email}`;
    const message = `<p>Dear ${reviewer},</p>
          <p>The feedback for the book chapter review has been ${resStatus}. Below are the details for your reference:</p>
                    <p><strong>Review:</strong></p><p> ${
                      comments.value[index].content
                    }
                    </p>
                    <p><strong>Content:</strong></p><p> ${
                      comments.value[index].selection
                    }</p>
                    <p><strong>Book:</strong> ${book.value.title}</p>
                    <p><strong>Chapter:</strong> ${
                      selectedChapterOutline.value.title
                    }</p>
                    <p><strong>${resStatus} By:</strong> ${
                      user.value?.displayName || user.value?.email
                    }</p>
                    <p><strong>${resStatus} Date:</strong> ${getHumanReadableDate(
                      Date.now(),
                    )}</p>
          <p> Please feel free to review the updated review comment.</p>
          <p>The Manuscriptr Team</p>`;

    if (!reviewer) return;

    sendEmailToUser(subject, message, reviewer);
  }
};

const getSelectedText = () => {
  if (typeof tinymce !== 'undefined') {
    const editor = tinymce.get('editor');
    tinymce.setActive(editor);
    const { selection } = editor;

    if (!selection) {
      selectedText.value = '';
      return;
    }
    const selectionText = selection.getContent({
      format: 'html',
    });
    selectedText.value = selectionText;
    return selection.getBookmark(2);
  } else {
    selectedText.value = '';
    return;
  }
};

const deleteComment = async (comment) => {
  const resolvedComment = await new Promise((resolve) => {
    $q.dialog({
      title: 'Delete the review?',
      message: 'Are you sure you want to delete this review?',
      ok: {
        color: 'primary',
      },
      cancel: {
        color: 'negative',
      },
    })
      .onOk(() => resolve(true))
      .onCancel(() => resolve(false));
  });
  if (!resolvedComment) return;

  const index = comments.value.findIndex((c) => c.id === comment.id);
  if (index !== -1) {
    const commentToDelete = comments.value[index];
    comments.value.splice(index, 1);

    if (reviewer === commentToDelete.user.email) return;

    const subject = `[Manuscriptr] Book Review Feedback Deleted by
      ${user.value?.displayName || user.value?.email}`;
    const message = `<p>Dear ${reviewer},</p>
          <p>The feedback for the book chapter review has been deleted. Below are the details for your reference:</p>
                    <p><strong>Review:</strong></p><p> ${
                      commentToDelete.content
                    }
                    </p>
                    <p><strong>Content:</strong></p><p> ${
                      commentToDelete.selection
                    }</p>
                    <p><strong>Book:</strong> ${book.value.title}</p>
                    <p><strong>Chapter:</strong> ${
                      selectedChapterOutline.value.title
                    }</p>
                    <p><strong>Deleted Date:</strong> ${getHumanReadableDate(
                      Date.now(),
                    )}</p>
          <p> Please feel free to review the updated review comment.</p>
          <p>The Manuscriptr Team</p>`;

    if (!reviewer) return;

    sendEmailToUser(subject, message, reviewer);
  } else showError($q, 'An error has occured. Review could not be deleted!');
  // saveBookProp('comments', comments);
};

const handleChatEnterPress = (event) => {
  if (event.ctrlKey || event.shiftKey) {
    const position = event.target.selectionStart;
    reply.value =
      reply.value.slice(0, position) + '\n' + reply.value.slice(position);
    nextTick(() => {
      event.target.selectionStart = position + 1;
      event.target.selectionEnd = position + 1;
    });
  }
};

const addReply = (id, isTyping = false) => {
  if (scrollChatRef.value) {
    nextTick(() => {
      const scrollTarget = scrollChatRef.value[0].getScrollTarget();
      const scrollHeight = scrollTarget.scrollHeight;
      const isAtBottom =
        scrollTarget.scrollTop + scrollTarget.clientHeight === scrollHeight;

      if (!isAtBottom) {
        scrollChatRef.value[0].setScrollPosition('vertical', scrollHeight, 300);
      }
    });
  }

  const index = comments.value.findIndex((comment) => comment.id === id);
  if (index !== -1) {
    if (isTyping) {
      comments.value[index].isTypings[userId.value] = {
        isTyping: reply.value,
        isTypingBy: userId.value,
        isTypingByName: user.value?.displayName || user.value?.email,
      };
    } else {
      comments.value[index].isTypings[userId.value] = {
        isTyping: false,
        isTypingBy: userId.value,
        isTypingByName: user.value?.displayName || user.value?.email,
      };
      comments.value[index].replies.push({
        createdAt: Date.now(),
        content: reply.value,
        userId: userId.value,
        readBy: [],
        user: {
          name: user.value?.displayName,
          email: user.value?.email,
        },
      });
      reply.value = '';

      const bookDetails = `
                      <p><strong>From:</strong></p><p> ${
                        user?.value?.displayName || user?.value?.email
                      }
                      <p><strong>Message:</strong></p><p> ${reply.value}
                    </p>
                    <p><strong>Content:</strong></p><p> ${
                      comments.value[index].selection
                    }</p>
                    <p><strong>Book:</strong> ${book.value.title}</p>
                    <p><strong>Chapter:</strong> ${
                      selectedChapterOutline.value.title
                    }</p>
                    <p><strong>Message Date:</strong> ${getHumanReadableDate(
                      Date.now(),
                    )}</p>
                    `;

      const sendChatNotifToUser = !isReviewMode.value
        ? userLoggedIn.value?.assignedAdminId
        : author?.value?.id;

      notificationStore.createUserNotification(sendChatNotifToUser as string, {
        title: `New chat message from ${
          user.value?.displayName || user.value?.email
        }`,
        notification: bookDetails,
        url: `/books/${book.value.id}`,
      });
    }
  }

  // saveBookProp('comments', comments);
};

const sendEmailToUser = (subject, message, toUser: any) => {
  try {
    const sendChatNotifToUser = !isReviewMode.value
      ? userLoggedIn.value?.assignedAdminId
      : author?.value?.id;

    isSending.value = true;
    isLoadingChapter.value = true;
    notificationStore.createUserNotification(sendChatNotifToUser as string, {
      title: subject,
      notification: message,
      url: `/books/${book.value.id}`,
    });

    sendEmail({
      to: toUser,
      from: user?.value?.email,
      subject: subject,
      html: message,
    }).then(async (response) => {
      isSending.value = false;
      isLoadingChapter.value = false;
      $q.notify({
        message: 'Message sent.',
        color: 'primary',
        timeout: 3000,
        icon: 'email',
      });
    });
  } catch (err) {
    isSending.value = false;
    isLoadingChapter.value = false;
    $q.notify({
      message: 'An error has occurred. Please try again.',
      color: 'red',
      timeout: 3000,
      icon: 'email',
    });
  }
};
const decodeHtmlEntities = (htmlContent) => {
  return decodeURIComponent(
    htmlContent.replace(/&([^;]+);/g, function (entity, match) {
      const charMap = {
        amp: '&',
        lt: '<',
        gt: '>',
        quot: '"',
        apos: "'",
        nbsp: ' ',
        // Add more character entities as needed
      };
      return charMap[match] || entity;
    }),
  );
};

const strippedContent = (str) => {
  const regex = /(<([^>]+)>)/gi;
  return str.replace(regex, '');
};

// Watchers
watch(slideReview, (newSlide, oldSlide) => {
  if (newSlide !== oldSlide) {
    slide.value = filteredComments.value.length - 1;
  }
});

watch(
  () => filteredComments.value,
  (newFC, oldFC) => {
    if (newFC.length !== oldFC.length) {
      slide.value = 0;
    }
  },
);

watch(
  () => slide.value,
  (newSlide, oldSlide) => {
    if (newSlide !== oldSlide && filteredComments.value.length) {
      selectComment(filteredComments.value[newSlide]);
      if (filteredComments.value[oldSlide]) {
        filteredComments.value[oldSlide].isTypings[user.value.uid] = {};
      }
      selectedComment.value = filteredComments.value[newSlide];
      reply.value = '';
    }
  },
  { immediate: true },
);
</script>
<style lang="scss">
#review {
  height: 100%;
}

#review .multi-line-truncate {
  display: -webkit-box;
  -webkit-line-clamp: 3; /* Number of lines to show */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 4.5em; /* Adjust based on the line height and number of lines */
  white-space: normal;
}
#review .drawer-list {
  max-height: calc(100vh - 134px);
  overflow: hidden;
}

#review .q-item .q-focus-helper {
  opacity: 0.05;
}

#review .reply {
  border: 1px solid rgba(0, 0, 0, 0.12);
}

#review .footnote {
  pointer-events: none;
}

.slide-review {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.splitter-container {
  position: relative; /* Container for sticky positioning */
  height: 100%; /* Full height to ensure scrolling */
  overflow: auto; /* Enable scrolling */
  padding-top: 0 !important;
  scroll-behavior: smooth;
}

.sticky-header {
  position: sticky;
  top: 0;
  padding-top: 10px;
  padding-bottom: 10px;
  background: white; /* Ensure the header has a solid background */
  z-index: 1; /* Ensure the header is above other content */
}
.resolve-btn {
  .q-checkbox__inner {
    color: #fff !important;
    font-size: 3rem;
  }
}
</style>
