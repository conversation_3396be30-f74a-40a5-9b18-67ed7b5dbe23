<template>
  <div class="text-h7" v-if="!noLabel">
    {{
      updating
        ? 'Edit your title'
        : "Let's write a working title! This can be edited later."
    }}
  </div>

  <q-input outlined v-model="title" label="Title">
    <template v-slot:after v-if="noLabel && titleOptions.length">
      <q-icon
        :name="!showMe ? 'visibility_off' : 'visibility'"
        @click="showMe = !showMe"
        clickable
      />
    </template>
    <template v-slot:append>
      <q-btn
        flat
        icon="img:robot.png"
        :loading="loading"
        :disable="loading"
        @click="generateTitle(false)"
      >
        <q-tooltip>Ask Manny</q-tooltip>
      </q-btn>
    </template>
  </q-input>
  <q-option-group
    :dense="noLabel"
    :options="titleOptions"
    v-model="title"
    v-if="showMe"
  />
</template>

<script setup lang="ts">
import { onMounted, computed, ref } from 'vue';
import { useVModel } from '@vueuse/core';
import { useQuasar } from 'quasar';
import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText, warn } from 'src/shared/lib/quasar-dialogs';
import { AutoBioGraphyMissionQuestions } from 'src/entities/book/model/types';

const props = withDefaults(
  defineProps<{
    modelValue?: string;
    questions?: AutoBioGraphyMissionQuestions;
    updating?: boolean;
    noLabel?: boolean;
  }>(),
  {
    updating: false,
  },
);
const emit = defineEmits<{
  'update:modelValue': [value: string];
}>();

const showMe = ref(true);
const title = useVModel(props, 'modelValue', emit);
const questions = useVModel(props, 'questions', emit);

const suggestedTitles = ref<string[]>([]);
const titleOptions = computed(() => {
  return suggestedTitles.value.map((title) => ({
    label: title.replace(/<[^>]*>/g, ''),
    value: title.replace(/<[^>]*>/g, ''),
  }));
});

const $q = useQuasar();

const loading = ref(false);

const results = ref('');
const prompt = computed(() => {
  return `
  <p>Main Reason: ${questions?.value?.mainReason}</p>
  <p>Book Focused: ${questions?.value?.bookFocused}</p>
  <p>Book Structure: ${questions?.value?.bookOrder}</p>
  <p>Key Message: ${questions?.value?.keyMessage}</p>
  <p>Gender: ${questions?.value?.gender}</p>
  <p>Age: ${questions?.value?.age}</p>
  <p>Description: ${questions?.value?.description}</p>

  <br />You are an award-winning ghostwriter who specializes in memoirs and autobiographies that move people to tears, laughter, and reflection. You know how to craft book titles that become instant bestsellers.
  <br />Using the details above, generate 3 emotionally resonant book titles for a memoir or autobiography. Follow these rules:

  <ul>
  <li>Each title must be either <strong>1 word</strong> or <strong>exactly 3 words</strong> — no two-word titles.</li>
  <li>Titles should reflect the heart of the author’s story — their emotional journey, transformation, or theme.</li>
  <li>Avoid clichés and generic phrases. Make them poetic, bold, or symbolic — something that would stand out on a bookshelf.</li>
  <li>Match the tone of the story: raw, inspirational, funny, heartbreaking, or healing.</li>
  <li>Focus on titles that sell books and grab attention — the kind that makes readers stop and want to pick it up.</li>
  </ul>`;
});
onMounted(async () => {
  if (title.value) {
    try {
      await generateTitle(true);
    } catch (e) {
      console.error(e);
    }
  }
});

async function generateTitle(invoking: boolean = false) {
  let promptRequest: string = prompt.value;
  if (title.value && !invoking) {
    const shouldDelete = await confirmOverrideText($q);
    if (!shouldDelete) {
      return;
    }
    results.value =
      typeof results.value === 'string' ? results.value : await results.value;
  }
  if (results.value) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results. ${results.value}`;
  }

  promptRequest = `${promptRequest} It should avoid gerunds.
      Ensure the titles are presented as plain text, without any numbering, bullets, or additional formatting.

      List of Titles:`;

  if (!invoking) loading.value = true;

  try {
    const response = await composeText(promptRequest);

    const titles = response
      .trim()
      .split('\n')
      .map((line) => line.replace(/^\d+\.\s*/, ''))
      .map((line) => line.replace(/^"|"$/g, ''))
      .filter((line) => line.length > 0);

    if (!invoking) {
      suggestedTitles.value = titles;
    }

    // save the results
    results.value = `${results.value} ${titles}`;
  } catch (e) {
    console.error(e);
    alert('An error occurred: ' + e);
  } finally {
    loading.value = false;
    showMe.value = true;
  }
}
</script>
