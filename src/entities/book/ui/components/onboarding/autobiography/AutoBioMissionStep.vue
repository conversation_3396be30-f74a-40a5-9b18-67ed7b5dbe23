<template>
  <q-card-section class="flex justify-between">
    <div class="text-h7">
      {{ updating ? 'Edit' : 'Write' }} your Autobiography Book
    </div>
  </q-card-section>
  <q-separator />

  <q-scroll-area
    :thumb-style="thumbStyle"
    :bar-style="barStyle"
    :style="{
      height: `calc(100vh - ${isScreenBiggerMd ? '390px' : '250px'})`,
    }"
  >
    <q-card-section>
      <section>
        <q-input
          label="What is the main reason you want to share your story?"
          v-model="mainReason"
          autogrow
          required
          outlined
        >
          <template v-slot:append>
            <q-btn
              flat
              icon="mic"
              @click="
                selectedField = 'mainReason';
                transcriptionDialog = true;
              "
            >
              <q-tooltip>Transcribe</q-tooltip>
            </q-btn>
          </template>
        </q-input>
      </section>

      <section class="q-mt-md">
        <q-select
          label="Is this book more legacy-focused, inspirational, educational, or transformational?"
          v-model="bookFocused"
          :options="bookFocusedOptions"
          required
          outlined
        />
      </section>

      <section class="q-mt-md">
        <q-select
          label="Do you want the order of your book to be chronological (life stages) or thematic (lessons, challenges, turning points)?"
          v-model="bookOrder"
          :options="bookOrderOptions"
          required
          outlined
        />
      </section>

      <section class="q-mt-md">
        <q-select
          label="Is your target audience a specific gender?"
          v-model="gender"
          :options="['Male', 'Female', 'Both']"
          required
          outlined
        />
      </section>
      <section class="q-mt-md">
        <q-select
          label="Is your target audience a specific age?"
          v-model="age"
          :options="ageOptions"
          multiple
          use-chips
          required
          outlined
        />
      </section>
      <section class="q-mt-md">
        <q-input
          label="Describe your target audience in a few words."
          v-model="description"
          autogrow
          required
          outlined
        >
          <template v-slot:append>
            <q-btn
              flat
              icon="mic"
              @click="
                selectedField = 'description';
                transcriptionDialog = true;
              "
            >
              <q-tooltip>Transcribe</q-tooltip>
            </q-btn>
          </template>
        </q-input>
      </section>
      <section class="q-mt-md">
        <q-input
          label="What key message or takeaway do you want readers to get from your book?"
          v-model="keyMessage"
          autogrow
          required
          outlined
        >
          <template v-slot:append>
            <q-btn
              flat
              icon="mic"
              @click="
                selectedField = 'keyMessage';
                transcriptionDialog = true;
              "
            >
              <q-tooltip>Transcribe</q-tooltip>
            </q-btn>

            <q-btn
              flat
              icon="img:robot.png"
              :loading="loadingKeyMessage"
              :disable="loadingKeyMessage"
              @click="generateKeyMessage(false)"
            >
              <q-tooltip>Ask Manny</q-tooltip>
            </q-btn>
          </template>
        </q-input>
      </section>
    </q-card-section>
  </q-scroll-area>

  <q-dialog v-model="transcriptionDialog">
    <BookTranscriptionApp
      @completed="completedTranscript"
      @close="transcriptionDialog = false"
    />
  </q-dialog>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue';
import { useVModels, toRefs } from '@vueuse/core';
import { useQuasar } from 'quasar';
import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText, warn } from 'src/shared/lib/quasar-dialogs';
import { barStyle, thumbStyle } from 'src/entities/setting';
import BookTranscriptionApp from '../../BookTranscriptionApp.vue';

const transcriptionDialog = ref(false);

export interface Questions {
  gender: 'Males' | 'Females' | 'Both';
  age: string[];
  keyMessage: string;
  mainReason: string;
  description: string;
  bookOrder: string;
  bookFocused: string;
}

const props = withDefaults(
  defineProps<{
    questions: Questions;
    updating?: boolean;
    isScreenBiggerMd?: boolean;
  }>(),
  {
    updating: false,
  },
);
const emit = defineEmits<{
  'update:questions': [value: Questions];
}>();
const { questions } = useVModels(props, emit);
const {
  gender,
  age,
  description,
  keyMessage,
  mainReason,
  bookOrder,
  bookFocused,
} = toRefs(questions);

const $q = useQuasar();

const ageConcat = new Intl.ListFormat('en', {
  style: 'long',
  type: 'conjunction',
});

enum Modes {
  Manual,
  Help,
}
const selectedField = ref('');
const mode = ref(Modes.Manual);
const completedTranscript = (data: any) => {
  const content = data.content;
  switch (selectedField.value) {
    case 'keyMessage':
      keyMessage.value = content;
      break;
    case 'description':
      description.value = content;
      break;
    case 'mainReason':
      mainReason.value = content;
      break;
  }
  transcriptionDialog.value = false;
};
const ageOptions = [
  'Any Age',
  'Under 18',
  '18-25',
  '25-35',
  '35-50',
  '50-65',
  '65+',
];
const bookOrderOptions = ['Chronological', 'Thematic'];
const bookFocusedOptions = [
  'Legacy-focused',
  'Inspirational',
  'Educational',
  'Transformational',
];

const loadingKeyMessage = ref(false);
async function generateKeyMessage(invoking: boolean = false) {
  if (
    (!gender.value || age.value.length === 0 || !description.value) &&
    !invoking
  ) {
    warn($q, {
      title: 'Missing fields',
      message: 'Please fill in all the previous fields first.',
    });
    return;
  }
  let promptRequest: string = updatedPrompt.value.keyMessage;
  if (keyMessage.value && !invoking) {
    const shouldDelete = await confirmOverrideText($q);

    if (!shouldDelete) {
      return;
    }
    results.value.keyMessage =
      typeof results.value.keyMessage === 'string'
        ? results.value.keyMessage
        : await results.value.keyMessage;
  }

  if (!invoking) loadingKeyMessage.value = true;
  if (results.value.keyMessage) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results or repeating the same sentences from the following results. ${results.value.keyMessage}`;
  }
  try {
    const response = await composeText(promptRequest);
    if (!invoking) keyMessage.value = response;
    // save the results
    results.value.keyMessage = `${results.value.keyMessage} ${response}`;
  } catch (e) {
    console.error(e);
    alert('An error occurred: ' + e);
  } finally {
    loadingKeyMessage.value = false;
  }
}

const ageGroups = computed(() => ageConcat.format(age.value));

// Updating of Open AI Prompt
const results = ref({} as { [key: string]: any });

const promptKeyMessage = computed(
  () => `
  <p>Main Reason: ${questions.value.mainReason}</p>
  <p>Book Structure: ${questions.value.bookOrder}</p>
  <p>Book Focused: ${questions.value.bookFocused}</p>
  <br/>
  You are an award-winning ghostwriter with decades of experience writing bestselling autobiographies and memoirs. Your specialty is helping new authors discover the heart of their story—the message that will resonate most deeply with readers.
  <br/>
  Based on the following details, write a single, emotionally compelling key message or takeaway that readers should remember after finishing this book:
  <ul>
  <li>Main reason for writing: ${mainReason.value}</li>
  <li>Book focus: ${bookFocused.value}</li>
  <li>Book structure: ${bookOrder.value}</li>
  </ul>
  Respond with 1–2 emotionally impactful sentences. The response should feel like a back cover hook or a central theme. Do not include bullet points, labels, or any formatting—just the raw text.
`,
);

onMounted(async () => {
  if (age.value.length) {
    mode.value = Modes.Help;
  }
  if (keyMessage.value) {
    try {
      await generateKeyMessage(true);
    } catch (e) {
      console.error(e);
    }
  }
});

const updatedPrompt = computed(() => ({
  keyMessage: promptKeyMessage.value,
}));
</script>

<style scoped></style>
