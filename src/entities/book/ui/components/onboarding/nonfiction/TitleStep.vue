<template>
  <div class="text-h7" v-if="!noLabel">
    {{
      updating
        ? 'Edit your title'
        : "Let's write a working title! This can be edited later."
    }}
  </div>

  <q-input outlined v-model="title" label="Title">
    <template v-slot:after v-if="noLabel && titleOptions.length">
      <q-icon
        :name="!showMe ? 'visibility_off' : 'visibility'"
        @click="showMe = !showMe"
        clickable
      />
    </template>
    <template v-slot:append>
      <q-btn
        flat
        icon="img:robot.png"
        :loading="loading"
        :disable="loading"
        @click="generateTitle(false)"
      >
        <q-tooltip>Ask Manny</q-tooltip>
      </q-btn>
    </template>
  </q-input>
  <q-option-group
    :dense="noLabel"
    :options="titleOptions"
    v-model="title"
    v-if="showMe"
  />
</template>

<script setup lang="ts">
import { onMounted, computed, ref } from 'vue';
import { useVModel } from '@vueuse/core';
import { useQuasar } from 'quasar';
import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText, warn } from 'src/shared/lib/quasar-dialogs';

const props = withDefaults(
  defineProps<{
    modelValue: string;
    mission: string;
    coreNeeds: string;
    updating?: boolean;
    noLabel?: boolean;
  }>(),
  {
    updating: false,
  },
);
const emit = defineEmits<{
  'update:modelValue': [value: string];
}>();

const showMe = ref(true);
const title = useVModel(props, 'modelValue', emit);

const suggestedTitles = ref<string[]>([]);
const titleOptions = computed(() => {
  return suggestedTitles.value.map((title) => ({
    label: title.replace(/<[^>]*>/g, ''),
    value: title.replace(/<[^>]*>/g, ''),
  }));
});

const $q = useQuasar();

const loading = ref(false);

const results = ref('');
const prompt = computed(() => {
  let promptStr: string;
  let promptVariables: string;
  if (props.coreNeeds && props.mission) {
    promptStr = `Write 3 book titles using the core needs and mission statement.
     Core Needs:\n ${props.coreNeeds} of the audience.
     Mission Statement:\n ${props.mission}
     Ensure each titles should only be written in 1 or 3 words (never use two word title) that capture the essence of the core needs and mission statement of the book.
It should provoke curiosity and be written in an interesting and compelling way.`;
  } else if (props.coreNeeds) {
    promptStr = `Write 3 book titles using the core needs "${props.coreNeeds}" of the audience.
     Ensure each titles should only be written in 1 or 3 words (never use two word title)  that capture the essence of the core needs and mission statement of the book.
It should provoke curiosity and be written in an interesting and compelling way.`;
    promptVariables = props.coreNeeds;
  } else {
    promptStr = `Write 3 book titles using the mission statement "${props.mission}".
     Ensure each titles should only be written in 1 or 3 words (never use two word title) that capture the essence of the mission statement of the book.
It should provoke curiosity and be written in an interesting and compelling way.`;
    promptVariables = props.mission;
  }
  return promptStr;
});
onMounted(async () => {
  if (title.value) {
    try {
      await generateTitle(true);
    } catch (e) {
      console.error(e);
    }
  }
});

async function generateTitle(invoking: boolean = false) {
  if (!props.mission && !invoking) {
    warn($q, {
      title:
        'Manny needs your mission statement to help give Title suggestions',
    });
    return;
  }
  let promptRequest: string = prompt.value;
  if (title.value && !invoking) {
    const shouldDelete = await confirmOverrideText($q);
    if (!shouldDelete) {
      return;
    }
    results.value =
      typeof results.value === 'string' ? results.value : await results.value;
  }
  if (results.value) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results. ${results.value}`;
  }

  promptRequest = `${promptRequest} It should avoid gerunds.
      Ensure the titles are presented as plain text, without any numbering, bullets, or additional formatting.

      List of Titles:`;

  if (!invoking) loading.value = true;
  try {
    const response = await composeText(promptRequest);

    const titles = response
      .trim()
      .split('\n')
      .map((line) => line.replace(/^\d+\.\s*/, ''))
      .map((line) => line.replace(/^"|"$/g, ''))
      .filter((line) => line.length > 0);
    if (!invoking) suggestedTitles.value = titles;
    // save the results
    results.value = `${results.value} ${titles}`;
  } catch (e) {
    console.error(e);
    alert('An error occurred: ' + e);
  } finally {
    loading.value = false;
    showMe.value = true;
  }
}
</script>
