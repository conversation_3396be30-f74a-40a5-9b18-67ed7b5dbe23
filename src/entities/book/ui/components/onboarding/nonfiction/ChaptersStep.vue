<template>
  <section>
    <q-card-section class="flex justify-between">
      <div class="text-h7">Chapters outline of your book:</div>
      <div>
        <q-btn
          color="primary"
          class="q-mr-sm"
          outline
          :disable="loadingChapters"
          @click="
            chapters.push({
              title: '',
              outline: '',
              wordCount: 0,
              number: chapters.length,
              isSection: false,
            })
          "
          label="Add a chapter"
        >
          <q-tooltip>Add a new chapter</q-tooltip>
        </q-btn>
        <q-btn
          @click="generateChapters(false)"
          :disable="loadingChapters"
          :loading="loadingChapters"
          outline
          color="primary"
        >
          Ask Manny
          <q-tooltip>Ask Manny</q-tooltip>
        </q-btn>
        <q-btn
          @click="$emit('complete')"
          color="primary"
          outline
          label="Save"
          icon-right="check"
          class="q-ml-sm"
          v-if="!isScreenBiggerMd"
        >
          <q-tooltip>Save Book Foundations?</q-tooltip>
        </q-btn>
      </div>
    </q-card-section>
    <q-separator />

    <q-scroll-area
      :thumb-style="thumbStyle"
      :bar-style="barStyle"
      :style="{
        height: `calc(100vh - ${isScreenBiggerMd ? '390px' : '250px'})`,
      }"
    >
      <q-card-section>
        <ul style="list-style: none" class="q-pa-none">
          <draggable v-model="chapters" item-key="">
            <template #item="{ element: chapter, index }" class="chapter">
              <li class="q-mb-sm">
                <q-input
                  v-model="chapters[index].title"
                  :loading="loadingChapters"
                  :disable="loadingChapters"
                  outlined
                  :label="'Chapter ' + (index + 1)"
                >
                  <template v-slot:before>
                    <q-icon name="menu" />
                  </template>

                  <template v-slot:append>
                    <q-btn
                      icon="delete"
                      color="danger"
                      flat
                      :disable="loadingChapters"
                      @click="deleteChapter(index)"
                    >
                      <q-tooltip>Delete Chapter</q-tooltip>
                    </q-btn>
                  </template>
                </q-input>
              </li>
            </template>
          </draggable>
        </ul>
      </q-card-section>
    </q-scroll-area>
  </section>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useVModel } from '@vueuse/core';
import { useQuasar } from 'quasar';
import draggable from 'vuedraggable';
import {
  confirmOverrideText,
  showError,
  warn,
} from 'src/shared/lib/quasar-dialogs';
import { composeText } from 'src/shared/api/openai';
import type { NewChapterOutline } from 'src/entities/book';
import { barStyle, thumbStyle } from 'src/entities/setting';

const props = defineProps<{
  modelValue: NewChapterOutline[];
  title: string;
  subtitle: string;
  isScreenBiggerMd?: boolean;
}>();
const emit = defineEmits<{
  'update:modelValue': [value: string[]];
  complete: [];
}>();

const chapters = useVModel(props, 'modelValue', emit);

const $q = useQuasar();

const loadingChapters = ref(false);
const deleteChapter = async (index: number) => {
  const shouldDelete = await confirmOverrideText($q, {
    message: `Are you sure you want to delete the chapter?`,
  });
  if (!shouldDelete) {
    return;
  }
  chapters.value.splice(index, 1);
};
const prompt = computed(() => {
  return `Using the book title and subtitle provided below, generate 9 chapter title outlines using the following criteria:
  1. provide a numbered list. For example: "1. Unveiling Prosperity's Blueprint"
  2. each chapter title should be within 4-6 words and be based on the promises and benefits identified in the title and subtitle. They should follow a structure flow/theme of getting started to mastery, and not repeated.
  3. titles should be based on the promises and benefits of the book identified in the title and subtitle
  4. using clever, compelling, eye catching language
  5. Please avoid any Markdown or HTML formatting in your response

  The provided information is as follows:
  Book title: ${props.title}
  Book subtitle: ${props.subtitle}`;
});

const results = ref('');

onMounted(async () => {
  if (chapters.value) {
    try {
      await generateChapters(true);
    } catch (e) {
      console.error(e);
    }
  }
});
async function generateChapters(invoking: boolean = false) {
  const { title, subtitle } = props;
  if ((!title || !subtitle) && !invoking) {
    warn($q, {
      title: 'Missing information',
      message: 'You must first fill the title, and subtitle',
    });
    return;
  }
  let promptRequest: string = prompt.value;
  if (chapters.value.length && !invoking) {
    const shouldDelete = await confirmOverrideText($q, {
      message:
        'This action will delete all chapters and their content that you have written. Are you sure you want to proceed?',
    });

    if (!shouldDelete) {
      return;
    }
    results.value =
      typeof results.value === 'string' ? results.value : await results.value;
  }
  if (!invoking) loadingChapters.value = true;
  if (results.value) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results or repeating the same sentences from the following results. ${results.value}`;
  }

  try {
    const response = await composeText(promptRequest);

    const newChapters: NewChapterOutline[] = response
      .trim()
      .split('\n')
      .filter((line) => /^\d+\./.test(line))
      .map((line) => line.replace(/^\d+\.\s*/, ''))
      .map((line) => line.replace(/^"|"$/g, ''))
      .filter((line) => line.length > 0)
      .map((chapter, idx) => ({
        title: chapter,
        wordCount: 0,
        isSection: false,
        number: idx + 1,
        outline: '',
      }));
    if (!invoking) chapters.value = newChapters;

    // save the results
    results.value = `${results.value} ${response}`;
  } catch (e) {
    console.error(e);
    showError($q, (e as Error).message);
  } finally {
    loadingChapters.value = false;
  }
}
</script>
