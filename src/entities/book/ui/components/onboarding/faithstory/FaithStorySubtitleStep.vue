<template>
  <div class="text-h7" v-if="!noLabel">
    {{
      updating
        ? 'Edit your subtitle'
        : "Let's write a working subtitle! This can be edited later."
    }}
  </div>

  <q-input outlined v-model="subtitle" label="Subtitle">
    <template v-slot:after v-if="noLabel && subtitleOptions.length">
      <q-icon
        :name="!showMe ? 'visibility_off' : 'visibility'"
        @click="showMe = !showMe"
        clickable
      />
    </template>
    <template v-slot:append>
      <UpdatePrompt
        v-if="canEditPrompt && !loading"
        :prompt="promptInit"
        :updatedprompt="updatedPrompt"
        name="subtitle"
        @update:prompt="updatePrompts"
        v-model:book="book"
      />
      <q-btn
        flat
        icon="img:robot.png"
        :loading="loading"
        :disable="loading"
        @click="generateSubtitle(false)"
      >
        <q-tooltip>Ask Manny</q-tooltip>
      </q-btn>
    </template>
  </q-input>
  <q-option-group
    :dense="noLabel"
    :options="subtitleOptions"
    v-model="subtitle"
    v-if="showMe"
  />
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { useVModel } from '@vueuse/core';
import { useQuasar } from 'quasar';
import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText, warn } from 'src/shared/lib/quasar-dialogs';
import { Book } from 'src/entities/book';
import { canEditPrompt } from 'src/entities/setting';
import UpdatePrompt from 'src/features/edit-prompt/ui/UpdatePrompt.vue';

const props = withDefaults(
  defineProps<{
    modelValue?: string;
    questions?: FaithStoryMissionQuestions;
    title?: string;
    updating?: boolean;
    noLabel?: boolean;
    book: Book;
  }>(),
  {
    updating: false,
  },
);
const emit = defineEmits<{
  'update:modelValue': [value: string];
  'update:book': [value: Book];
}>();
const $q = useQuasar();

const showMe = ref(true);
const subtitle = useVModel(props, 'modelValue', emit);
const book = useVModel(props, 'book', emit);
const questions = useVModel(props, 'questions', emit);

const suggestedSubtitles = ref<string[]>([]);
const subtitleOptions = computed(() => {
  return suggestedSubtitles.value.map((title) => ({
    label: title,
    value: title,
  }));
});

const promptInit = computed(() => {
  return `
  <p>Main Reason for Sharing Story: ${questions?.value?.mainReason}</p>
  <p>Type of Faith Story: ${
    questions?.value?.otherBestDescription || questions?.value?.bestDescription
  }</p>
  <p>Story Structure Preference: ${questions?.value?.bookOrder}</p>
  <p>Spiritual Background: ${questions?.value?.bookFocused}</p>
  <p>Target Audience Description: ${questions?.value?.description}</p>
  <p>Key Life Events or Spiritual Moments: ${questions?.value
    ?.keyLifeEvents}</p>
  <p>Reader Takeaway: ${questions?.value?.readersTakeaway}</p>
  <p>Book Goals: ${questions?.value?.authorImpact}</p>
  <p>Gender: ${questions?.value?.gender}</p>
  <p>Age: ${questions?.value?.age}</p>

  <br />You are an award-winning writer who specializes in emotionally resonant faith-based memoirs. You know how to craft subtitles that capture the spiritual transformation within a story and make readers feel an instant connection.
  <br />Using the details above, generate 3 powerful subtitle options for the memoir titled ${
    props.title
  }. Follow these guidelines:

  <ul>
  <li>Each subtitle should highlight 1–3 key themes, spiritual insights, or emotional takeaways from the author’s journey.</li>
  <li>Avoid clichés and vague phrasing - be specific, poetic, and meaningful.</li>
  <li>Keep the language emotionally compelling, with a rhythm that feels like a real book subtitle.</li>
  <li>Whenever possible, avoid gerunds (words ending in -ing).</li>
  <li>Subtitles should reflect why someone would want to read this book - to feel seen, to find hope, or to connect with something spiritually profound.</li>
  </ul>`;
});

const updatedPromptComp = computed(() => {
  return book.value.promptEdits?.subtitle?.value || promptInit.value;
});

// updating prompts
const updatedPrompt = ref(updatedPromptComp.value);

function updatePrompts(newPrompt) {
  updatedPrompt.value = newPrompt.value;
}

const prompt = computed(() => updatedPrompt.value);
const loading = ref(false);

const results = ref('');

onMounted(async () => {
  if (subtitle.value) {
    try {
      await generateSubtitle(true);
    } catch (e) {
      console.error(e);
    }
  }
});

async function generateSubtitle(invoking: boolean = false) {
  if (!props.title && !invoking) {
    warn($q, {
      message: 'Manny needs your Title to help give Subtitle Suggestions',
    });
    return;
  }
  let promptRequest: string = prompt.value;
  if (subtitle.value && !invoking) {
    const shouldDelete = await confirmOverrideText($q);

    if (!shouldDelete) {
      return;
    }
    results.value =
      typeof results.value === 'string' ? results.value : await results.value;
  }
  if (results.value) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results. ${results.value}`;
  }
  promptRequest = `${promptRequest} Desired Format - A phrase that does not include a semi colon followed by other text or phrase, avoiding any Markdown or HTML formatting in your response

        List three subtitles:`;
  if (!invoking) loading.value = true;
  try {
    const openAIOptions =
      book.value?.promptEdits?.subtitle?.openAIOptions || {};
    const response = await composeText(promptRequest, openAIOptions);

    const subtitles = response
      .trim()
      .split('\n')
      .map((line) => line.replace(/^\d+\.\s*/, ''))
      .map((line) => line.replace(/^"|"$/g, ''))
      .filter((line) => line.length > 0);
    if (!invoking) suggestedSubtitles.value = subtitles;
    // save the results
    results.value = `${results.value} ${subtitles}`;
  } catch (e) {
    console.error(e);
    alert('An error occurred: ' + e);
  } finally {
    loading.value = false;
    showMe.value = true;
  }
}
</script>
