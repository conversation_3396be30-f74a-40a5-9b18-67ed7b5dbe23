<template>
  <div class="text-h7" v-if="!noLabel">
    {{
      updating
        ? 'Edit your title'
        : "Let's write a working title! This can be edited later."
    }}
  </div>

  <q-input outlined v-model="title" label="Title">
    <template v-slot:after v-if="noLabel && titleOptions.length">
      <q-icon
        :name="!showMe ? 'visibility_off' : 'visibility'"
        @click="showMe = !showMe"
        clickable
      />
    </template>
    <template v-slot:append>
      <UpdatePrompt
        v-if="canEditPrompt && !loading"
        :prompt="prompt"
        :updatedprompt="updatedPrompt"
        name="title"
        @update:prompt="updatePrompts"
        v-model:book="book"
      />
      <q-btn
        flat
        icon="img:robot.png"
        :loading="loading"
        :disable="loading"
        @click="generateTitle(false)"
      >
        <q-tooltip>Ask Manny</q-tooltip>
      </q-btn>
    </template>
  </q-input>
  <q-option-group
    :dense="noLabel"
    :options="titleOptions"
    v-model="title"
    v-if="showMe"
  />
</template>

<script setup lang="ts">
import { onMounted, computed, ref } from 'vue';
import { useVModel } from '@vueuse/core';
import { useQuasar } from 'quasar';
import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText, warn } from 'src/shared/lib/quasar-dialogs';
import {
  Book,
  FaithStoryMissionQuestions,
} from 'src/entities/book/model/types';
import { canEditPrompt } from 'src/entities/setting';
import UpdatePrompt from 'src/features/edit-prompt/ui/UpdatePrompt.vue';

const props = withDefaults(
  defineProps<{
    modelValue?: string;
    book: Book;
    questions?: FaithStoryMissionQuestions;
    updating?: boolean;
    noLabel?: boolean;
  }>(),
  {
    updating: false,
  },
);
const emit = defineEmits<{
  'update:modelValue': [value: string];
  'update:book': [value: Book];
}>();

const showMe = ref(true);
const title = useVModel(props, 'modelValue', emit);
const questions = useVModel(props, 'questions', emit);
const book = useVModel(props, 'book', emit);

const suggestedTitles = ref<string[]>([]);
const titleOptions = computed(() => {
  return suggestedTitles.value.map((title) => ({
    label: title.replace(/<[^>]*>/g, ''),
    value: title.replace(/<[^>]*>/g, ''),
  }));
});

const $q = useQuasar();

const loading = ref(false);

const results = ref('');
const prompt = ref(`
  <p>Main Reason for Sharing Story: ${questions?.value?.mainReason}</p>
  <p>Type of Faith Story: ${
    questions?.value?.otherBestDescription || questions?.value?.bestDescription
  }</p>
  <p>Story Structure Preference: ${questions?.value?.bookOrder}</p>
  <p>Spiritual Background: ${questions?.value?.bookFocused}</p>
  <p>Target Audience Description: ${questions?.value?.description}</p>
  <p>Key Life Events or Spiritual Moments: ${questions?.value
    ?.keyLifeEvents}</p>
  <p>Reader Takeaway: ${questions?.value?.readersTakeaway}</p>
  <p>Book Goals: ${questions?.value?.authorImpact}</p>
  <p>Gender: ${questions?.value?.gender}</p>
  <p>Age: ${questions?.value?.age}</p>


  <br />You are an award-winning faith-based memoir specialist who crafts emotionally powerful titles that move readers to tears, hope, and reflection. You know how to create book titles that resonate deeply and become instant bestsellers.
  <br />Using the details above, generate 3 emotionally resonant book titles for a faith-based memoir or autobiography. Follow these rules:

  <ul>
  <li>Each title must be either <strong>1 word</strong> or <strong>exactly 3 words</strong> — no two-word titles.</li>
  <li>Titles should reflect the heart of the author's faith journey - their spiritual transformation, emotional trials, or key life themes.</li>
  <li>Avoid clichés and generic phrases. Make them poetic, bold, or symbolic - something that would stand out on a bookshelf.</li>
  <li>Match the tone of the story: raw, inspirational, hopeful, heartbreaking, or redemptive.</li>
  <li>Focus on titles that sell books and grab attention - the kind that makes readers stop and want to pick it up.</li>
  </ul>`);

const updatedPromptComp = computed(() => {
  return book.value?.promptEdits?.title?.value || prompt.value;
});

// Updating of Open AI Prompt
// title

// updating prompts
const updatedPrompt = ref(updatedPromptComp.value);
function updatePrompts(newPrompt) {
  updatedPrompt.value = newPrompt.value;
}

onMounted(async () => {
  if (title.value) {
    try {
      await generateTitle(true);
    } catch (e) {
      console.error(e);
    }
  }
});

async function generateTitle(invoking: boolean = false) {
  let promptRequest: string = prompt.value;
  if (title.value && !invoking) {
    const shouldDelete = await confirmOverrideText($q);
    if (!shouldDelete) {
      return;
    }
    results.value =
      typeof results.value === 'string' ? results.value : await results.value;
  }
  if (results.value) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results. ${results.value}`;
  }

  promptRequest = `${promptRequest} It should avoid gerunds.
      Ensure the titles are presented as plain text, without any numbering, bullets, or additional formatting.

      List of Titles:`;

  if (!invoking) loading.value = true;

  try {
    const openAIOptions = book.value?.promptEdits?.title?.openAIOptions || {};
    const response = await composeText(promptRequest, openAIOptions);

    const titles = response
      .trim()
      .split('\n')
      .map((line) => line.replace(/^\d+\.\s*/, ''))
      .map((line) => line.replace(/^"|"$/g, ''))
      .filter((line) => line.length > 0);

    if (!invoking) {
      suggestedTitles.value = titles;
    }

    // save the results
    results.value = `${results.value} ${titles}`;
  } catch (e) {
    console.error(e);
    alert('An error occurred: ' + e);
  } finally {
    loading.value = false;
    showMe.value = true;
  }
}
</script>
