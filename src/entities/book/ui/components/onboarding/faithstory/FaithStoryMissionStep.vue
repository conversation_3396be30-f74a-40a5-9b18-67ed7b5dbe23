<template>
  <q-card-section class="flex justify-between">
    <div class="text-h7">
      {{ updating ? 'Edit' : 'Write' }} your Faith Story Book
    </div>
  </q-card-section>
  <q-separator />

  <q-scroll-area
    :thumb-style="thumbStyle"
    :bar-style="barStyle"
    :style="{
      height: `calc(100vh - ${isScreenBiggerMd ? '390px' : '250px'})`,
    }"
  >
    <q-card-section>
      <section>
        <q-select
          label="Is your target audience a specific gender?"
          v-model="gender"
          :options="['Male', 'Female', 'Both']"
          required
          outlined
        />
      </section>
      <section class="q-mt-md">
        <q-select
          label="Is your target audience a specific age?"
          v-model="age"
          :options="ageOptions"
          multiple
          use-chips
          required
          outlined
        />
      </section>

      <section class="q-mt-md">
        <q-input
          label="What is the main reason you want to share your faith story?"
          v-model="mainReason"
          :placeholder="
            mainReason
              ? ''
              : 'Examples: Inspire others, share a testimony, help people going through similar struggles'
          "
          autogrow
          required
          outlined
        >
          <template v-slot:append>
            <q-btn
              flat
              icon="mic"
              @click="
                selectedField = 'mainReason';
                transcriptionDialog = true;
              "
            >
              <q-tooltip>Transcribe</q-tooltip>
            </q-btn>
          </template>
        </q-input>
      </section>

      <section class="q-mt-md">
        <q-select
          label="Which best describes your faith story?"
          v-model="bestDescription"
          :options="bestDescriptionOptions"
          required
          outlined
        />
        <q-input
          class="q-mt-md"
          v-if="bestDescription === 'Other'"
          label="If other, please specify"
          v-model="otherBestDescription"
          autogrow
          outlined
        >
          <template v-slot:append>
            <q-btn
              flat
              icon="mic"
              @click="
                selectedField = 'bestDescription';
                transcriptionDialog = true;
              "
            >
              <q-tooltip>Transcribe</q-tooltip>
            </q-btn>
          </template>
        </q-input>
      </section>

      <section class="q-mt-md">
        <q-select
          label="Do you want to tell your story in order of life events or grouped by themes?"
          v-model="bookOrder"
          :options="bookOrderOptions"
          required
          outlined
        >
          <template v-slot:hint>
            <span
              v-if="!bookOrder && focusedField === 'bookOrder'"
              class="text-grey-6"
            >
              like lessons, challenges, or turning points
            </span>
          </template>
        </q-select>
      </section>

      <section class="q-mt-md">
        <q-input
          label="Is your story focused on a specific faith or spiritual background?"
          v-model="bookFocused"
          :placeholder="
            bookFocused
              ? ''
              : 'Examples: Christian, Muslim, New Age, Spiritual but not religious'
          "
          autogrow
          required
          outlined
        >
          <template v-slot:append>
            <q-btn
              flat
              icon="mic"
              @click="
                selectedField = 'bookFocused';
                transcriptionDialog = true;
              "
            >
              <q-tooltip>Transcribe</q-tooltip>
            </q-btn>
          </template>
        </q-input>
      </section>

      <section class="q-mt-md">
        <q-input
          label="Describe your target audience in a few words."
          v-model="description"
          :placeholder="
            description
              ? ''
              : 'Examples: Women struggling with doubt, men leaving prison, teens in crisis, fellow believers'
          "
          autogrow
          required
          outlined
        >
          <template v-slot:append>
            <q-btn
              flat
              icon="mic"
              @click="
                selectedField = 'description';
                transcriptionDialog = true;
              "
            >
              <q-tooltip>Transcribe</q-tooltip>
            </q-btn>
          </template>
        </q-input>
      </section>

      <section class="q-mt-md">
        <q-input
          label="What are 3-5 key life events or spiritual moments you want to include?"
          v-model="keyLifeEvents"
          autogrow
          required
          outlined
        >
          <template v-slot:append>
            <q-btn
              flat
              icon="mic"
              @click="
                selectedField = 'keyLifeEvents';
                transcriptionDialog = true;
              "
            >
              <q-tooltip>Transcribe</q-tooltip>
            </q-btn>
            <UpdatePrompt
              v-if="canEditPrompt && !loadingKeyLifeEvents"
              :prompt="promptKeyLifeEvents"
              :updatedprompt="updatedPrompt.keyLifeEvents"
              name="keyLifeEvents"
              @update:prompt="updatePrompts"
              v-model:book="book"
            />
            <q-btn
              flat
              icon="img:robot.png"
              :loading="loadingKeyLifeEvents"
              :disable="loadingKeyLifeEvents"
              @click="generateKeyLifeEvents(false)"
            >
              <q-tooltip>Ask Manny</q-tooltip>
            </q-btn>
          </template>
        </q-input>
      </section>

      <section class="q-mt-md">
        <q-input
          label="What do you hope readers will take away from your story?"
          v-model="readersTakeaway"
          autogrow
          required
          outlined
          :placeholder="
            description
              ? ''
              : 'Examples: Hope, belief in miracles, deeper faith, encouragement'
          "
        >
          <template v-slot:append>
            <q-btn
              flat
              icon="mic"
              @click="
                selectedField = 'readersTakeaway';
                transcriptionDialog = true;
              "
            >
              <q-tooltip>Transcribe</q-tooltip>
            </q-btn>
            <UpdatePrompt
              v-if="canEditPrompt && !loadingReadersTakeaway"
              :prompt="promptReadersTakeaway"
              :updatedprompt="updatedPrompt.readersTakeaway"
              name="readersTakeaway"
              @update:prompt="updatePrompts"
              v-model:book="book"
            />
            <q-btn
              flat
              icon="img:robot.png"
              :loading="loadingReadersTakeaway"
              :disable="loadingReadersTakeaway"
              @click="generateReadersTakeaway(false)"
            >
              <q-tooltip>Ask Manny</q-tooltip>
            </q-btn>
          </template>
        </q-input>
      </section>

      <section class="q-mt-md">
        <q-input
          label="What are 2-3 things you hope this book does for you?"
          v-model="authorImpact"
          :placeholder="
            authorImpact
              ? ''
              : 'Examples: Healing, leaving a legacy, grow a platform, lead others to God'
          "
          autogrow
          required
          outlined
        >
          <template v-slot:append>
            <q-btn
              flat
              icon="mic"
              @click="
                selectedField = 'authorImpact';
                transcriptionDialog = true;
              "
            >
              <q-tooltip>Transcribe</q-tooltip>
            </q-btn>
            <UpdatePrompt
              v-if="canEditPrompt && !loadingAuthorImpact"
              :prompt="promptAuthorImpact"
              :updatedprompt="updatedPrompt.authorImpact"
              name="authorImpact"
              @update:prompt="updatePrompts"
              v-model:book="book"
            />
            <q-btn
              flat
              icon="img:robot.png"
              :loading="loadingAuthorImpact"
              :disable="loadingAuthorImpact"
              @click="generateAuthorImpact(false)"
            >
              <q-tooltip>Ask Manny</q-tooltip>
            </q-btn>
          </template>
        </q-input>
      </section>
    </q-card-section>
  </q-scroll-area>

  <q-dialog v-model="transcriptionDialog">
    <BookTranscriptionApp @completed="completedTranscript" @close="" />
  </q-dialog>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue';
import { useVModels, toRefs } from '@vueuse/core';
import { useQuasar } from 'quasar';
import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText, warn } from 'src/shared/lib/quasar-dialogs';
import { barStyle, canEditPrompt, thumbStyle } from 'src/entities/setting';
import BookTranscriptionApp from '../../BookTranscriptionApp.vue';
import UpdatePrompt from 'src/features/edit-prompt/ui/UpdatePrompt.vue';
import { Book } from 'src/entities/book';

const transcriptionDialog = ref(false);
const focusedField = ref('');

export interface Questions {
  gender: 'Males' | 'Females' | 'Both';
  age: string[];
  keyLifeEvents: string;
  readersTakeaway: string;
  mainReason: string;
  bestDescription: string;
  otherBestDescription: string;
  description: string;
  bookOrder: string;
  bookFocused: string;
  authorImpact: string;
}

const props = withDefaults(
  defineProps<{
    book: Book;
    questions: Questions;
    updating?: boolean;
    isScreenBiggerMd?: boolean;
  }>(),
  {
    updating: false,
  },
);
const emit = defineEmits<{
  'update:questions': [value: Questions];
  'update:book': [value: Book];
}>();
const { questions, book } = useVModels(props, emit);
const {
  gender,
  age,
  description,
  keyLifeEvents,
  readersTakeaway,
  authorImpact,
  bestDescription,
  otherBestDescription,
  mainReason,
  bookOrder,
  bookFocused,
} = toRefs(questions);

const $q = useQuasar();

const ageConcat = new Intl.ListFormat('en', {
  style: 'long',
  type: 'conjunction',
});

enum Modes {
  Manual,
  Help,
}
const selectedField = ref('');
const mode = ref(Modes.Manual);
const completedTranscript = (data: any) => {
  const content = data.content;
  switch (selectedField.value) {
    case 'keyLifeEvents':
      keyLifeEvents.value = content;
      break;
    case 'description':
      description.value = content;
      break;
    case 'mainReason':
      mainReason.value = content;
      break;
  }
  transcriptionDialog.value = false;
};
const ageOptions = [
  'Any Age',
  'Under 18',
  '18-25',
  '25-35',
  '35-50',
  '50-65',
  '65+',
];
const bestDescriptionOptions = [
  'A personal testimony of coming to faith',
  'A story of transformation through faith',
  'A series of faith-based life lessons',
  'A story of overcoming adversity with faith',
  'Other',
];
const bookOrderOptions = ['Chronological', 'Thematic'];

const loadingKeyLifeEvents = ref(false);
async function generateKeyLifeEvents(invoking: boolean = false) {
  if (
    (!gender.value ||
      age.value.length === 0 ||
      !description.value ||
      !mainReason.value ||
      !bestDescription.value ||
      (bestDescription.value === 'Other' && !otherBestDescription.value)) &&
    !invoking
  ) {
    warn($q, {
      title: 'Missing fields',
      message: 'Please fill in all the previous fields first.',
    });
    return;
  }
  let promptRequest: string = updatedPrompt.value.keyLifeEvents;
  if (keyLifeEvents.value && !invoking) {
    const shouldDelete = await confirmOverrideText($q);

    if (!shouldDelete) {
      return;
    }
    results.value.keyLifeEvents =
      typeof results.value.keyLifeEvents === 'string'
        ? results.value.keyLifeEvents
        : await results.value.keyLifeEvents;
  }

  if (!invoking) loadingKeyLifeEvents.value = true;
  if (results.value.keyLifeEvents) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results or repeating the same sentences from the following results. ${results.value.keyLifeEvents}`;
  }
  try {
    const openAIOptions =
      book.value?.promptEdits?.keyLifeEvents?.openAIOptions || {};
    const response = await composeText(promptRequest, openAIOptions);
    if (!invoking) keyLifeEvents.value = response;
    // save the results
    results.value.keyLifeEvents = `${results.value.keyLifeEvents} ${response}`;
  } catch (e) {
    console.error(e);
    alert('An error occurred: ' + e);
  } finally {
    loadingKeyLifeEvents.value = false;
  }
}

const loadingAuthorImpact = ref(false);
async function generateAuthorImpact(invoking: boolean = false) {
  if (
    (!gender.value ||
      age.value.length === 0 ||
      !description.value ||
      !mainReason.value ||
      !keyLifeEvents.value ||
      !readersTakeaway.value ||
      (bestDescription.value === 'Other' && !otherBestDescription.value)) &&
    !invoking
  ) {
    warn($q, {
      title: 'Missing fields',
      message: 'Please fill in all the previous fields first.',
    });
    return;
  }
  let promptRequest: string = updatedPrompt.value.authorImpact;
  if (authorImpact.value && !invoking) {
    const shouldDelete = await confirmOverrideText($q);

    if (!shouldDelete) {
      return;
    }
    results.value.authorImpact =
      typeof results.value.authorImpact === 'string'
        ? results.value.authorImpact
        : await results.value.authorImpact;
  }

  if (!invoking) loadingAuthorImpact.value = true;
  if (results.value.authorImpact) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results or repeating the same sentences from the following results. ${results.value.keyLifeEvents}`;
  }
  try {
    const openAIOptions =
      book.value?.promptEdits?.authorImpact?.openAIOptions || {};
    const response = await composeText(promptRequest, openAIOptions);
    if (!invoking) authorImpact.value = response;
    // save the results
    results.value.authorImpact = `${results.value.authorImpact} ${response}`;
  } catch (e) {
    console.error(e);
    alert('An error occurred: ' + e);
  } finally {
    loadingAuthorImpact.value = false;
  }
}

const loadingReadersTakeaway = ref(false);
async function generateReadersTakeaway(invoking: boolean = false) {
  if (
    (!gender.value ||
      age.value.length === 0 ||
      !description.value ||
      !mainReason.value ||
      !keyLifeEvents.value ||
      (bestDescription.value === 'Other' && !otherBestDescription.value)) &&
    !invoking
  ) {
    warn($q, {
      title: 'Missing fields',
      message: 'Please fill in all the previous fields first.',
    });
    return;
  }
  let promptRequest: string = updatedPrompt.value.readersTakeaway;
  if (readersTakeaway.value && !invoking) {
    const shouldDelete = await confirmOverrideText($q);

    if (!shouldDelete) {
      return;
    }
    results.value.readersTakeaway =
      typeof results.value.readersTakeaway === 'string'
        ? results.value.readersTakeaway
        : await results.value.readersTakeaway;
  }

  if (!invoking) loadingReadersTakeaway.value = true;
  if (results.value.readersTakeaway) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results or repeating the same sentences from the following results. ${results.value.keyLifeEvents}`;
  }
  try {
    const openAIOptions =
      book.value?.promptEdits?.readersTakeaway?.openAIOptions || {};
    const response = await composeText(promptRequest, openAIOptions);
    if (!invoking) readersTakeaway.value = response;
    // save the results
    results.value.readersTakeaway = `${results.value.readersTakeaway} ${response}`;
  } catch (e) {
    console.error(e);
    alert('An error occurred: ' + e);
  } finally {
    loadingReadersTakeaway.value = false;
  }
}

const ageGroups = computed(() => ageConcat.format(age.value));

// Updating of Open AI Prompt
const results = ref({} as { [key: string]: any });

const promptKeyLifeEvents = computed(
  () => `
  <p>Main Reason for Sharing Story: ${questions?.value?.mainReason}</p>
  <p>Type of Faith Story: ${
    questions?.value?.otherBestDescription || questions?.value?.bestDescription
  }</p>
  <p>Story Structure Preference: ${questions?.value?.bookOrder}</p>
  <p>Spiritual Background: ${questions?.value?.bookFocused}</p>
  <p>Target Audience Description: ${questions?.value?.description}</p>
  <p>Gender: ${questions?.value?.gender}</p>
  <p>Age: ${questions?.value?.age}</p>

  <br/>
  Using the details above, identify 3–5 powerful life events or spiritual moments that the author might include in their faith-based story. Follow these guidelines:
  <br/>
  <ul>
  <li>Each response should be emotionally resonant or spiritually significant</li>
  <li>Use plain language and keep each response under 12 words</li>
  <li>Do not separate by age band or use bullet points</li>
  <li>List each response on a new line without numbers, bullets, or breaks</li>
  </ul>`,
);

const promptAuthorImpact = computed(
  () => `
  <p>Main Reason for Sharing Story: ${questions?.value?.mainReason}</p>
  <p>Type of Faith Story: ${
    questions?.value?.otherBestDescription || questions?.value?.bestDescription
  }</p>
  <p>Story Structure Preference: ${questions?.value?.bookOrder}</p>
  <p>Spiritual Background: ${questions?.value?.bookFocused}</p>
  <p>Target Audience Description: ${questions?.value?.description}</p>
  <p>Key Life Events or Spiritual Moments: ${questions?.value
    ?.keyLifeEvents}</p>
  <p>Desired Reader Takeaway: ${questions?.value?.readersTakeaway}</p>
  <p>Gender: ${questions?.value?.gender}</p>
  <p>Age: ${questions?.value?.age}</p>

  <br/>
  Using the details above, identify 2–3 personal or professional goals the author may hope to achieve by writing this faith-based book. Follow these guidelines: <br/>
  <ul>
  <li>Keep each goal under 12 words</li>
  <li>Focus on emotional healing, spiritual impact, or personal purpose</li>
  <li>Include practical goals only if they’re meaningful to the author’s faith journey</li>
  <li>List each response on a new line without numbers, bullets, or breaks</li>
  </ul>`,
);

const promptReadersTakeaway = computed(
  () => `
  <p>Main Reason for Sharing Story: ${questions?.value?.mainReason}</p>
  <p>Type of Faith Story: ${
    questions?.value?.otherBestDescription || questions?.value?.bestDescription
  }</p>
  <p>Story Structure Preference: ${questions?.value?.bookOrder}</p>
  <p>Spiritual Background: ${questions?.value?.bookFocused}</p>
  <p>Target Audience Description: ${questions?.value?.description}</p>
  <p>Key Life Events or Spiritual Moments: ${questions?.value
    ?.keyLifeEvents}</p>
  <p>Gender: ${questions?.value?.gender}</p>
  <p>Age: ${questions?.value?.age}</p>

  <br/>
  Using the details above, identify 3–4 key takeaways the reader should gain from this faith-based story. Follow these guidelines:
   <br/>
  <ul>
  <li>Use short, emotionally meaningful sentences under 12 words</li>
  <li>Focus on spiritual, emotional, or personal growth themes</li>
  <li>Align takeaways with the author’s faith background and target audience</li>
  <li>List each response on a new line without numbers, bullets, or breaks</li>
  </ul>`,
);

onMounted(async () => {
  if (age.value.length) {
    mode.value = Modes.Help;
  }
  if (keyLifeEvents.value) {
    try {
      await generateKeyLifeEvents(true);
    } catch (e) {
      console.error(e);
    }
  }
  if (authorImpact.value) {
    try {
      await generateAuthorImpact(true);
    } catch (e) {
      console.error(e);
    }
  }
  if (readersTakeaway.value) {
    try {
      await generateReadersTakeaway(true);
    } catch (e) {
      console.error(e);
    }
  }
});

const updatedPromptComp = computed(() => {
  return {
    keyLifeEvents:
      book.value.promptEdits?.keyLifeEvents?.value || promptKeyLifeEvents.value,
    authorImpact:
      book.value.promptEdits?.authorImpact?.value || promptAuthorImpact.value,
    readersTakeaway:
      book.value.promptEdits?.readersTakeaway?.value ||
      promptReadersTakeaway.value,
  };
});

const updatedPrompt = computed(() => ({
  keyLifeEvents: updatedPromptComp.value.keyLifeEvents,
  authorImpact: updatedPromptComp.value.authorImpact,
  readersTakeaway: updatedPromptComp.value.readersTakeaway,
}));

function updatePrompts(newPrompt) {
  updatedPrompt.value[newPrompt.name] = newPrompt.value;
}
</script>

<style scoped>
:deep(.q-field__prepend) {
  margin-left: -4px;
  margin-right: -4px;
}
</style>
