<template>
  <div class="q-pl-none q-pr-xs q-gutter-md">
    <TitleStep
      v-model="book.title"
      :mission="book.mission"
      :core-needs="book.missionQuestions.need"
      :updating="false"
      :noLabel="true"
      v-if="book.title && (book.mission || book.missionQuestions.need)"
    />
    <q-input
      v-else
      v-model="book.title"
      :disable="readonly"
      label="Title"
      type="text"
      outlined
    />
    <SubtitleStep
      v-if="
        book.title &&
        book.subtitle &&
        (book.mission || book.missionQuestions.readerImpact)
      "
      v-model="book.subtitle"
      :mission="book.mission"
      :title="book.title"
      :transformations="book.missionQuestions.readerImpact"
      :updating="false"
      :noLabel="true"
    />
    <q-input
      v-else
      v-model="book.subtitle"
      :disable="readonly"
      label="Subtitle"
      type="text"
      outlined
    />
    <q-input
      v-model="book.authorName"
      :disable="readonly"
      label="Author Name"
      type="text"
      class="q-mb-md"
      outlined
    />
  </div>
  <q-btn
    label="Book Foundations"
    icon="auto_stories"
    color="primary"
    align="center"
    @click="emits('edit-mission')"
  />
</template>

<script lang="ts" setup>
import { type Book } from 'src/entities/book';
import { useVModels } from '@vueuse/core';
import SubtitleStep from 'src/entities/book/ui/components/onboarding/nonfiction/SubtitleStep.vue';
import TitleStep from 'src/entities/book/ui/components/onboarding/nonfiction/TitleStep.vue';

const props = defineProps<{
  book: Book;
  readonly?: boolean;
}>();

const emits = defineEmits<{
  'edit-mission': [];
  'update:book': [book: Book];
}>();

const { book } = useVModels(props, emits);
</script>

<style lang="scss"></style>
