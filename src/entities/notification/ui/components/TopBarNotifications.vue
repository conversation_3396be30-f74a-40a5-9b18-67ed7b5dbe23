<template>
  <q-btn
    :dense="isTopNavDefault"
    :stretch="isTopNavDefault"
    :color="!isTopNavDefault ? 'primary' : ''"
    :flat="isTopNavDefault"
    :round="!isTopNavDefault"
    :padding="!isTopNavDefault ? '13px' : ''"
    icon="notifications"
    :class="{
      'animate-bell':
        newNotification && notificationStore.notifications.length > 0,
      'q-mr-xs': !isTopNavDefault,
    }"
    @click="clearNotificationAnimation"
  >
    <q-badge color="red" floating>{{ notificationStore.unreadCount }}</q-badge>
    <q-menu @focus="clearNotificationAnimation">
      <q-list
        :style="{
          maxWidth: `${$q.screen.sizes.sm}px !important`,
          minWidth: `${$q.screen.sizes.sm - 300}px !important`,
        }"
      >
        <q-item
          v-close-popup
          v-for="(notification, index) in notificationStore.notifications"
          :key="notification.id"
          clickable
          v-ripple
          @click="notificationStore.goToNotificationUrl(notification)"
          v-if="notificationStore.notifications.length"
        >
          <q-item-section>
            <q-item-label overline class="text-bold" lines="1">{{
              notification.title
            }}</q-item-label>
            <q-item-label
              caption
              lines="2"
              v-html="notification.notification"
            />
          </q-item-section>

          <q-item-section side top>
            <q-item-label caption>{{
              formatDate(notification.createdAt)
            }}</q-item-label>
            <q-icon
              :name="
                notification.isRead ? 'mark_email_read' : 'mark_email_unread'
              "
              color="primary"
            />
          </q-item-section>
        </q-item>

        <q-item v-else style="min-width: 20vw">
          <q-item-section>
            <q-item-label overline class="text-bold self-center"
              >No notifications found!</q-item-label
            >
          </q-item-section>
        </q-item>
        <q-separator spaced />
        <q-item
          clickable
          @click="notificationStore.goToNotifications"
          v-close-popup
        >
          <q-item-section>
            <q-item-label class="text-primary self-center"
              >View All
            </q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </q-menu>
  </q-btn>
</template>

<script setup lang="ts">
import { onUnmounted, ref, watch } from 'vue';
import {
  useNotificationStore,
  UserNotification,
} from 'src/entities/notification';
import { user } from 'src/entities/user';
import { formatDate, isTopNavDefault } from 'src/entities/setting';
import { useQuasar } from 'quasar';

//  state
const $q = useQuasar();

/** Store for managing notifications */
const notificationStore = useNotificationStore();

/** List of user notifications */
const notifications = ref<UserNotification[]>([]);

/** Fetch notifications for the user */
(async () => {
  await notificationStore.fetchNotifications(user?.value?.uid as string, 5);
  notifications.value = notificationStore.notifications;
})();

/** Count of unread notifications */
const unreadCount = ref(0);

/** Fetch count of new user notifications */
(async () => {
  await notificationStore.countNewUserNotifications(user?.value?.uid as string);
  unreadCount.value = notificationStore.unreadCount;
})();

/** State to track if there is a new notification */
const newNotification = ref(unreadCount.value > 0);

/** Notification sound setup */
const notificationSound = new Audio('/audio/pop.mp3');
notificationSound.volume = 0;

/**
 * Plays notification sound
 */
const playSound = () => {
  notificationSound
    .play()
    .catch((error: any) => console.error('Sound play error:', error));
};

/**
 * Clears notification animation and resets sound
 */
const clearNotificationAnimation = () => {
  playSound();
  notificationSound.volume = 0;
  newNotification.value = false;
};

/** Watch for changes in notification store */
watch(
  notificationStore,
  (newValue, oldValue) => {
    if (newValue.unreadCount > 0 && notificationSound) {
      notificationSound.volume = 1;
      newNotification.value = true;
      playSound();
    }
  },
  { immediate: true },
);

/** Cleanup function when component is unmounted */
onUnmounted(() => {
  notificationStore.stopListeningNewNotificationLimit();
  notificationStore.stopListeningNewNotification();
});
</script>

<style scoped>
@keyframes bell-ring {
  0%,
  100% {
    transform: rotate(0);
  }
  25% {
    transform: rotate(-10deg);
  }
  50% {
    transform: rotate(10deg);
  }
  75% {
    transform: rotate(-10deg);
  }
}

.animate-bell {
  animation: bell-ring 0.5s ease-in-out infinite;
}
</style>
