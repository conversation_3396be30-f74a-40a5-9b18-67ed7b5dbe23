<template>
  <q-card flat bordered class="column">
    <q-card-section horizontal class="justify-between">
      <q-card-section class="q-pt-xs">
        <div class="text-overline">Buy Now!</div>
        <div class="text-h5 q-mt-sm q-mb-xs">{{ package.name }}</div>
        <div class="text-caption text-grey">
          {{ package.description }}
        </div>
      </q-card-section>
      <q-card-section>
        <q-img class="rounded-borders" src="robot.png" width="62px" />
      </q-card-section>
    </q-card-section>

    <q-separator />
    <div v-for="price in package.prices" :key="price.id">
      <PackageCardPrice :price="price" v-if="price.type === 'recurring'" />
    </div>
  </q-card>
</template>

<script setup lang="ts">
import type { Package } from '../model/package';
import PackageCardPrice from './PackageCardPrice.vue';

defineProps<{ package: Package }>(); // Defining props for the Vue component
</script>

<style></style>
