<template>
  <q-item-section horizontal v-if="isLoadingProduct">
    <q-skeleton type="text" />
    <q-skeleton type="text" class="text-subtitle1" />
    <q-skeleton type="text" width="100%" class="text-subtitle1" />
    <q-skeleton type="text" width="80%" class="text-subtitle1" />
    <q-skeleton type="text" class="text-caption" />
  </q-item-section>
  <q-item-section horizontal v-else-if="errorProduct">
    <p>An error occurred: {{ errorProduct }}</p>
  </q-item-section>
  <q-item-section
    horizontal
    class="block col-md-12"
    v-else-if="product != null"
  >
    <q-card flat>
      <q-card-section horizontal class="flex justify-between">
        <q-list>
          <q-item>
            <q-item-section>
              <q-item-label>
                <h4>
                  {{ product.metadata.appName ?? product.name }}
                </h4></q-item-label
              >
              <q-item-label caption lines="2">{{
                product.metadata.subscriptionDetails ?? product.description
              }}</q-item-label>
            </q-item-section>
          </q-item>

          <q-item>
            <q-item-section>
              <p>
                <b>Started:</b>
                {{ toLocaleDate(subscription.current_period_start) }}
              </p>

              <div
                v-if="
                  userLoggedIn &&
                  userLoggedIn.stripeId &&
                  !userLoggedIn.paypalId
                "
              >
                <p class="q-mb-sm">
                  <b>Current billing period:</b>
                  {{ toLocaleDate(subscription.current_period_start) }}
                  &ndash;
                  {{ toLocaleDate(subscription.current_period_end) }}
                </p>
                <p v-if="price" v-html="billingAmount" />
              </div>
            </q-item-section>
          </q-item>
        </q-list>
        <q-card-actions vertical class="justify-around q-px-md">
          <q-btn
            flat
            color="red"
            icon="delete"
            v-if="subscription.status === 'active' && userLoggedIn?.stripeId"
            @click="cancel"
            :disable="cancelSubscriptionAction[subscription.id]"
            :loading="cancelSubscriptionAction[subscription.id]"
          >
            <q-tooltip>Cancel Subscription </q-tooltip></q-btn
          >
          <q-btn flat color="green" icon="verified_user">
            <q-tooltip>Active Subscription </q-tooltip>
          </q-btn>

          <q-btn
            flat
            color="primary"
            @click="portalLink"
            target="_blank"
            icon="fa-brands fa-stripe"
            v-if="userLoggedIn.stripeId"
            :disable="!linkToStripe"
          >
            <q-tooltip>Update Stripe Payment </q-tooltip>
          </q-btn>
        </q-card-actions>
      </q-card-section>
    </q-card>
  </q-item-section>
</template>

<script setup lang="ts">
import { useAsyncState } from '@vueuse/core';
import {
  cancelSubscription,
  getPackage,
  getPriceInfo,
  getSubscriptionInvoiceDetails,
  goToPortalLink,
} from '../api';
import { type Price, Subscription } from '@stripe/firestore-stripe-payments';
import { computed, ref, watch, onMounted } from 'vue';
import {
  confirmDeletion,
  confirmOverrideText,
  showError,
} from 'src/shared/lib/quasar-dialogs';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { cancelSubscriptionAction } from 'src/entities/package';
import { userLoggedIn } from 'src/entities/user';

const emits = defineEmits<{
  cancelled: [];
}>();

// Define component props with a type
const props = defineProps<{ subscription: Subscription }>();
// Reactive reference for loading state
const isLoading = ref(false);
// Access to the Vue Router instance
const router = useRouter();
// Access to Quasar framework instance
const $q = useQuasar();
// Reactive reference for link storage
const linkToStripe = ref('');

// Async state for fetching product details
const {
  state: product,
  isLoading: isLoadingProduct,
  error: errorProduct,
} = useAsyncState(getPackage(props.subscription.product), null);

// Async state for fetching price information
const {
  state: price,
  isLoading: isLoadingPrice,
  error: errorPrice,
} = useAsyncState(
  getPriceInfo(props.subscription.product, props.subscription.price),
  null,
);

// Invoice details state
const invoiceDetails = ref({
  showCancelButton: true,
  amountToPay: price?.unit_amount!,
  isLoading: false,
});

// Computed property to determine the status color based on subscription status
const statusColor = computed(() => {
  const { status } = props.subscription;
  switch (status) {
    case 'active':
      return 'positive';
    case 'trialing':
      return 'warning';
    default:
      return 'negative';
  }
});

/**
 * Opens a new tab with the Stripe portal link.
 */
const portalLink = async (): Promise<void> => {
  window.open(linkToStripe.value, '_blank');
};

// Fetch the Stripe portal link on component mount
onMounted(async () => {
  if (userLoggedIn?.value?.stripeId)
    linkToStripe.value = await goToPortalLink(userLoggedIn?.value?.stripeId);
});

/**
 * Converts a timestamp string to a locale date string.
 * @param timestamp - The timestamp to convert.
 * @returns The locale date string.
 */
const toLocaleDate = (timestamp: string): string => {
  return new Date(timestamp).toLocaleDateString();
};

// Watch for price loading changes to update invoice details
watch(isLoadingPrice, async () => {
  if (
    !isLoadingPrice.value &&
    (price.value?.metadata?.allowCancelWhenInvoicePaid ||
      !price.value?.unit_amount)
  ) {
    try {
      invoiceDetails.value = await getSubscriptionInvoiceDetails(
        props.subscription,
        price.value as Price,
      );
      if (invoiceDetails.value.amountToPay) {
        price.value.unit_amount = invoiceDetails.value.amountToPay;
      }
    } catch (e) {
      console.log('error retrieving invoices');
    }
  }
});

// Computed property for formatted price amount
const priceAmount = computed(() => {
  return Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'usd',
  }).format(
    (price.value?.unit_amount || invoiceDetails.value.amountToPay || 0) / 100,
  );
});

const billingAmount = computed(() => {
  if (props.subscription.status === 'trialing') {
    return '';
  } else {
    return `<b>Billed at</b> ${priceAmount.value} every  ${price.value?.interval_count} ${price.value?.interval}(s)`;
  }
});

/**
 * Cancels the subscription and handles UI updates.
 */
const cancel = async (): Promise<void> => {
  isLoading.value = true;
  confirmOverrideText($q, {
    message: `Are you sure you want to cancel your subscription to Manuscriptr?`,
  })
    .then(
      async () => {
        cancelSubscriptionAction.value[props.subscription.id] = true;
        $q.notify({
          message:
            'We are still processing your cancellation request. Please wait.',
          color: 'primary',
          multiLine: true,
          avatar: 'robot.png',
        });
        const isCancelled = await cancelSubscription(
          userLoggedIn.value?.stripeId,
        );
        // const isCancelled = true;
        if (!isCancelled) {
          await showError($q, 'Cancellation could not be processed.');
          isLoading.value = false;
          delete cancelSubscriptionAction.value[props.subscription.id];
        } else {
          $q.dialog({
            title: 'Cancellation Successful',
            message: 'Your subscription to Manuscriptr has been cancelled.',
            persistent: true,
            ok: {
              color: 'primary',
            },
          }).onOk(() => {
            isLoading.value = false;
            location.reload();
          });

          emits('cancelled');
        }
      },
      () => {
        isLoading.value = false;
        delete cancelSubscriptionAction.value[props.subscription.id];
      },
    )
    .catch(() => {})
    .finally(() => {
      isLoading.value = false;
    });
};
</script>
