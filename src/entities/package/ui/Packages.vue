<template>
  <section class="q-pa-lg q-mx-auto" id="packages" style="max-width: 1024px">
    <q-list>
      <q-item>
        <q-item-section>
          <q-item-label overline
            ><h2 class="text-black">
              Available subscription packages:
            </h2></q-item-label
          >
        </q-item-section>

        <q-item-section side top>
          <q-icon name="verified" color="primary" />
        </q-item-section>
      </q-item>

      <q-separator spaced />
      <q-item>
        <q-item-section v-if="isLoadingPackages">
          <div class="row items-stretch q-col-gutter-md">
            <div class="col-xs-12 col-md-4" v-for="n in 3" :key="n">
              <q-card>
                <q-item>
                  <q-item-section>
                    <q-item-label>
                      <q-skeleton type="text" class="text-caption"
                    /></q-item-label>
                    <q-item-label caption lines="2"
                      ><q-skeleton
                        type="text"
                        width="100%"
                        class="text-subtitle1"
                    /></q-item-label>
                    <q-item-label caption lines="2"
                      ><q-skeleton
                        type="text"
                        width="80%"
                        class="text-subtitle1"
                    /></q-item-label>
                  </q-item-section>

                  <q-item-section side top>
                    <q-skeleton type="QAvatar" />
                  </q-item-section>
                </q-item>
                <q-item>
                  <q-item-section>
                    <q-skeleton type="text" />
                    <q-skeleton type="text" class="text-subtitle1" />
                    <q-skeleton
                      type="text"
                      width="100%"
                      class="text-subtitle1"
                    />
                  </q-item-section>
                </q-item>
                <q-card-actions align="stretch" class="q-gutter-md">
                  <q-skeleton type="QBtn" />
                  <q-skeleton type="QBtn" />
                </q-card-actions>
              </q-card>
            </div>
          </div>
        </q-item-section>

        <q-item-section v-else-if="errorPacakges">
          An error has occurred: {{ errorPacakges }}
        </q-item-section>
        <q-item-section v-else-if="packages.length">
          <div
            class="row items-stretch q-col-gutter-md q-mt-xs"
            v-for="product in packages"
            :key="product.id"
          >
            <div class="col-xs-12 col-md-4" v-if="product.prices?.length">
              <PackageCard :package="product" />
            </div>
          </div>
        </q-item-section>
        <q-item-section v-else>
          <UnsubscribedNotice
            message="You no longer have an active subscription"
          />
        </q-item-section>
      </q-item>
    </q-list>
  </section>
</template>

<script setup lang="ts">
import { useAsyncState } from '@vueuse/core';
import { getPackages, PackageCard } from 'src/entities/package';
import UnsubscribedNotice from 'src/entities/package/ui/UnsubscribedNotice.vue'; // Importing a Vue component

const {
  state: packages, // State holding the list of packages
  isLoading: isLoadingPackages, // Boolean state indicating if packages are still loading
  error: errorPacakges, // State holding any errors that occurred during fetching packages
} = useAsyncState(getPackages(), []);
</script>

<style lang="scss"></style>
