<template>
  <q-dialog
    v-model="compareDialog"
    persistent
    transition-show="scale"
    transition-hide="scale"
    :maximized="maximizedToggle"
  >
    <q-card style="min-width: 60vw; overflow: hidden">
      <q-card-section class="q-pa-none">
        <q-inner-loading
          :showing="
            hasChangesOnThread === true || hasChangesOnThread === undefined
          "
          color="primary"
          label="Hang tight, Saving...."
          label-class="text-primary"
          label-style="font-size: 1.1em"
          style="z-index: 999"
        >
          <q-spinner-dots color="primary" size="2em" />
          <div class="align-center" v-if="!errorThread">
            <q-icon name="img:robot.png" class="q-ml-xs" size="xl" />
            Hang tight, Loading....
          </div>
          <div class="align-center" v-else>
            <q-icon name="img:robot.png" class="q-ml-xs" size="xl" />
            An error has occured, please refresh the page and try again!
          </div>
        </q-inner-loading>
        <q-toolbar class="bg-primary text-white q-pa-sm" style="z-index: 2">
          <q-btn flat @click="toggleLeftDrawer" round dense icon="menu" />
          <q-icon name="img:robot.png" size="md" />
          <q-toolbar-title
            class="single-line-title"
            shrink
            v-html="
              `Chat with Manny: ${selectedChapterOutline.title.replace(
                /<[^>]*>/g,
                '',
              )}`
            "
          />
          <q-space />

          <q-btn
            dense
            flat
            :icon="!maximizedToggle ? 'fullscreen' : 'fullscreen_exit'"
            @click="maximizedToggle = !maximizedToggle"
            v-if="1 != 1"
          >
            <q-tooltip v-if="!maximizedToggle" class="bg-white text-primary"
              >Maximize</q-tooltip
            >
          </q-btn>
          <q-btn
            dense
            flat
            icon="close"
            v-if="!openAIStore.isLoading"
            v-close-popup
          >
            <q-tooltip class="bg-white text-primary">Close</q-tooltip>
          </q-btn>
        </q-toolbar>

        <q-drawer
          v-model="leftDrawerOpen"
          bordered
          :mini="miniState"
          show-if-above
          :width="300"
          :breakpoint="500"
        >
          <!-- List of saved ChatGPT chats -->

          <q-list v-if="chatDBStore.chatDb.chats.length !== 0">
            <template v-for="chat in groupedChats" :key="chat.id">
              <q-item
                v-if="chat.isSeparator"
                class="separator-item"
                :style="{
                  cursor: openAIStore.isLoading
                    ? 'not-allowed !important'
                    : 'default',
                }"
              >
                <!-- Timeline marker -->
                <q-item-section>
                  <q-item-label class="text-subtitle2">{{
                    chat.label
                  }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-item
                v-else
                clickable
                v-ripple
                @click="handleSelectChatItem(chat.id)"
                :active="chatDBStore.chatDb.selectedChatId === chat.id"
                active-class="selected-chat"
              >
                <q-item-section avatar style="margin-right: 5px">
                  <q-icon name="message" />
                </q-item-section>
                <q-item-section>
                  <q-item-label class="ellipsis truncate">{{
                    getShortHumanReadableDate(chat.createdAt)
                  }}</q-item-label>
                </q-item-section>
                <q-menu
                  touch-position
                  context-menu
                  transition-show="jump-down"
                  transition-hide="jump-up"
                  class="popup-styled"
                >
                  <!-- Popup menu -->
                  <q-list dense style="min-width: 100px">
                    <q-item
                      clickable
                      v-close-popup
                      @click="showDeleteChatDialog(chat.id, chat.name)"
                    >
                      <q-item-section avatar>
                        <q-icon name="delete" class="text-red" />
                      </q-item-section>
                      <q-item-section class="text-red">Delete</q-item-section>
                    </q-item>
                  </q-list>
                </q-menu>
              </q-item>
            </template>

            <q-separator class="my-separator" inset />
            <!-- Button to start a new chat -->

            <q-item
              clickable
              v-ripple
              @click="handleNewChat"
              v-if="openAIStore.canStillChat"
              :style="{
                cursor: openAIStore.isLoading
                  ? 'not-allowed !important'
                  : 'default',
              }"
            >
              <q-item-section avatar>
                <q-icon name="add" />
              </q-item-section>
              <q-item-section>New chat</q-item-section>
            </q-item>
            <!-- Button to clear the chat -->
            <q-item
              clickable
              v-ripple
              @click="showClearChatsDialog"
              :style="{
                cursor: openAIStore.isLoading
                  ? 'not-allowed !important'
                  : 'default',
              }"
            >
              <q-item-section avatar>
                <q-icon name="delete" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Clear conversations</q-item-label>
              </q-item-section>
            </q-item>
            <!-- Button to open the settings window here -->
            <q-item
              clickable
              v-if="1 != 1"
              v-ripple
              @click="showSettingsDialog"
            >
              <q-item-section avatar>
                <q-icon name="settings" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Settings</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-drawer>

        <ChatComponent
          :book="book"
          :selectedChapterOutline="selectedChapterOutline"
          :isLoadingChapter="isLoadingChapter"
        />
      </q-card-section>
      <q-card-section>
        <SendComponent v-if="hasChangesOnThread === false" />
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import ChatComponent from 'src/entities/chapterchat/ui/components/ChatComponent.vue';
import { Book, ChapterOutline, getOutline } from 'src/entities/book';
import { useVModels } from '@vueuse/core/index';
import { computed, onMounted, ref, watch } from 'vue';

//@ts-ignore
import vueDraggableResizableTs from 'vue-draggable-resizable-ts';
import {
  loadSettings,
  settings,
  useChatDBStore,
  useOpenAIStore,
} from 'src/entities/chapterchat';
import { date, useQuasar } from 'quasar';
import {
  ClearChatsDialog,
  DeleteChatDialog,
} from 'src/entities/chapterchat/ui/components';
import SettingsDialog from 'src/entities/chapterchat/ui/components/SettingsDialog.vue';
import SendComponent from 'src/entities/chapterchat/ui/components/SendComponent.vue';
import { getHumanReadableDate, getShortHumanReadableDate } from '../../setting';
import { userLoggedIn } from 'src/entities/user';
import { useDebounceFn } from '@vueuse/core';

const props = defineProps<{
  book: Book;
  selectedChapterOutline: ChapterOutline;
  isLoadingChapter: boolean;
  editorContent: string;
  toggleChat: boolean;
  hasChangesOnThread: boolean;
}>();
const emits = defineEmits<{
  'update:toggleChat': [toggleChat: boolean];
  'update:selectedChapterOutline': [selectedChapterOutline: ChapterOutline];
}>();

const {
  isLoadingChapter,
  book,
  selectedChapterOutline,
  toggleChat,
  editorContent,
  hasChangesOnThread,
} = useVModels(props, emits);

const compareDialog = ref(toggleChat.value);

const maximizedToggle = ref(true);
const errorThread = ref(false);

const leftDrawerOpen = ref(true);
const miniState = ref(false);
const drawer = ref(true);

function toggleLeftDrawer() {
  miniState.value = !miniState.value;
  if ($q.screen.sm || $q.screen.xs)
    leftDrawerOpen.value = !leftDrawerOpen.value;
  else leftDrawerOpen.value = true;
}

const openAIStore = useOpenAIStore();
openAIStore.bookId = book.value.id;
openAIStore.chapterId = selectedChapterOutline.value.id;

const chatDBStore = useChatDBStore();
chatDBStore.bookId = book.value.id;
chatDBStore.chapterId = selectedChapterOutline.value.id;

const $q = useQuasar();
const search = ref('');

watch(toggleChat, (newVal, oldVal) => {
  compareDialog.value = newVal;
});
watch(compareDialog, (newVal, oldVal) => {
  toggleChat.value = newVal;
  $q.dark.set(newVal);
});

const handleGetChats = async () => {
  return await chatDBStore.fetchChats();
};

watch(
  hasChangesOnThread,
  useDebounceFn(async (newContent, oldContent) => {
    hasChangesOnThread.value =
      hasChangesOnThread.value === undefined ? true : hasChangesOnThread.value;
    console.log('hasChangesOnThread:' + hasChangesOnThread.value);
    errorThread.value = false;
    if (
      userLoggedIn.value?.features?.includes('chatwithmanny') &&
      hasChangesOnThread.value === true
    ) {
      openAIStore.bookId = book.value.id;
      openAIStore.chapterId = selectedChapterOutline.value.id;
      chatDBStore.bookId = book.value.id;
      chatDBStore.chapterId = selectedChapterOutline.value.id;
      chatDBStore.chatDb.selectedChatId = (await handleGetChats()) as string;

      if (
        selectedChapterOutline?.value?.wordCount &&
        selectedChapterOutline?.value?.wordCount > 250 &&
        selectedChapterOutline?.value?.assistantbot?.isUpdate !== false
      ) {
        console.log('invoke assistant');
        const success = await openAIStore.loadChapterContentForChat(
          editorContent.value,
        );
        if (success === true) {
          selectedChapterOutline.value = await getOutline(
            book.value.id,
            selectedChapterOutline.value.id,
          );

          hasChangesOnThread.value = false;
        } else {
          hasChangesOnThread.value = false;
          errorThread.value = true;
        }
      } else {
        hasChangesOnThread.value = false;
      }
    }
  }, 2000),
);

watch(selectedChapterOutline, async (newId, oldId) => {
  if (newId.id !== oldId.id) {
    openAIStore.bookId = book.value.id;
    openAIStore.chapterId = selectedChapterOutline.value.id;
    chatDBStore.bookId = book.value.id;
    chatDBStore.chapterId = selectedChapterOutline.value.id;
    chatDBStore.chatDb.selectedChatId = (await handleGetChats()) as string;

    if (chatDBStore.chatDb.selectedChatId)
      await handleSelectChatItem(chatDBStore.chatDb.selectedChatId);
    else await handleSelectChat(chatDBStore.chatDb.selectedChatId);
  }
});

const onSearchInput = async () => {
  await chatDBStore.fetchChats(search.value);
};

onMounted(async () => {
  loadSettings(); // Load settings

  // Show settings dialog if API key is missing
  if (!settings?.model?.value?.trim()) {
    showSettingsDialog();
  }

  chatDBStore.chatDb.selectedChatId = await handleGetChats();
  if (chatDBStore.chatDb.selectedChatId)
    await handleSelectChatItem(chatDBStore.chatDb.selectedChatId);
  else await handleSelectChat(chatDBStore.chatDb.selectedChatId);
});

const handleNewChat = async () => {
  await chatDBStore.createChat(null);
  closeDrawerIfOverlay();
};

const handleSelectChat = async (chatId) => {
  chatDBStore.chatDb.selectedChatId = chatId;
  await chatDBStore.createChat(chatId);
  closeDrawerIfOverlay();
};

const handleSelectChatItem = async (chatId) => {
  chatDBStore.chatDb.selectedChatId = chatId;
  await chatDBStore.selectChat(chatId);
  closeDrawerIfOverlay();
};

// Groups chats by last modified date into timeline categories
const groupedChats = computed(() => {
  const today = date.startOfDate(new Date(), 'day');
  const yesterday = date.addToDate(today, { days: -1 });
  const sevenDaysAgo = date.addToDate(today, { days: -7 });
  const thirtyDaysAgo = date.addToDate(today, { days: -30 });

  const result = [];
  let lastSeparator = null;

  chatDBStore.chatDb.chats.forEach((chat) => {
    const chatDate = date.startOfDate(chat.lastModified, 'day');

    let label;
    if (chatDate >= today) {
      label = 'Today';
    } else if (chatDate >= yesterday) {
      label = 'Yesterday';
    } else if (chatDate >= sevenDaysAgo) {
      label = 'Previous 7 days';
    } else if (chatDate >= thirtyDaysAgo) {
      label = 'Previous 30 days';
    } else {
      label = date.formatDate(chat.lastModified, 'MMMM YYYY');
    }

    if (lastSeparator !== label) {
      result.push({ isSeparator: true, label });
      lastSeparator = label;
    }

    result.push(chat);
  });

  return result;
});

const saveModel = () => {
  $q.localStorage.setItem('model', settings.model.value);
};

function showDeleteChatDialog(chatId, chatName) {
  $q.dialog({
    component: DeleteChatDialog,
    componentProps: {
      chatId: chatId,
      chatName: chatName,
    },
  });
}

function showClearChatsDialog() {
  $q.dialog({
    component: ClearChatsDialog,
    // componentProps: {},
  });
}

function showSettingsDialog() {
  closeDrawerIfOverlay();
  $q.dialog({
    component: SettingsDialog,
    // componentProps: {},
  });
}

// Close the left drawer menu if in overlay mode
function closeDrawerIfOverlay() {
  // Close the left drawer menu if the screen is small
  if ($q.screen.sm || $q.screen.xs) {
    leftDrawerOpen.value = false;
  }
}
</script>
<style>
.q-drawer--left {
  top: 0px !important;
  height: 100vh;
  z-index: 1;
}
.q-layout__shadow {
  display: none !important;
}
.single-line-title {
  white-space: nowrap; /* Ensures text stays on one line */
  overflow: hidden; /* Hides overflowed content */
  text-overflow: ellipsis; /* Adds an ellipsis (...) for truncated text */
}
</style>
