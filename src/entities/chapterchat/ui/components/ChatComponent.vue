<template>
  <q-scroll-area
    ref="scrollAreaRef"
    style="height: calc(100vh - 160px); width: 100%"
    :thumb-style="thumbStyle"
    :bar-style="barStyle"
  >
    <div v-if="chatDBStore.chatDb?.messages?.length !== 0">
      <MessagesComponent :messages="chatDBStore.chatDb.messages || []" />
    </div>
    <div
      v-else
      class="flex flex-center self-end"
      :style="{ 'margin-top': `${marginTop}px`, width: '100%' }"
    >
      <div>
        <!-- Logo in the center -->
        <div class="q-pb-xl row flex-center">
          <q-img
            src="robot.png"
            alt="robot.png"
            style="width: 55px; height: 55px"
          />
        </div>

        <div class="row q-pl-md q-col-gutter-md tip-button-container">
          <div
            class="col-6 col-sm-3"
            v-for="button in TipButtons"
            :key="button.label"
          >
            <q-btn
              :icon="button.icon"
              :label="button.label"
              class="tip-button"
              no-caps
              @click="() => handleTipBtnAction(button.label)"
            />
          </div>
        </div>
      </div>
    </div>
  </q-scroll-area>
</template>

<script setup lang="ts">
import { nextTick, ref, watch } from 'vue';
import MessagesComponent from './MessagesComponent.vue';
import { barStyle, thumbStyle } from 'src/entities/setting';
import { useVModels } from '@vueuse/core/index';
import { useChatDBStore, useOpenAIStore } from 'src/entities/chapterchat';
import { Book, ChapterOutline } from 'src/entities/book';

const props = defineProps<{
  book: Book;
  selectedChapterOutline: ChapterOutline;
  isLoadingChapter: boolean;
}>();

const { isLoadingChapter, book, selectedChapterOutline } = useVModels(props);
const openAIStore = useOpenAIStore();
openAIStore.bookId = book.value.id;
openAIStore.chapterId = selectedChapterOutline.value.id;

const chatDBStore = useChatDBStore();

const scrollAreaRef = ref(null);

const marginTop = ref(0);

const TipButtons = [
  {
    icon: 'speed', // for pacing
    label: 'Give me tips to improve the pacing of my chapter',
  },
  {
    icon: 'landscape', // for setting/world-building
    label: 'Suggest ideas to build a vivid setting or environment',
  },
  {
    icon: 'favorite', // for emotional depth
    label: 'Help me deepen the emotional impact of this chapter',
  },
  {
    icon: 'auto_stories', // for strong chapter endings
    label: 'Suggest a powerful chapter ending to leave a lasting impression',
  },
];

const handleTipBtnAction = async (label) => {
  // isImageGeneration.value = false;
  await openAIStore.sendMessage(label);
};

// Watch for changes in the selectedChatId
watch(
  chatDBStore.chatDb,
  async (newId, oldId) => {
    if (scrollAreaRef.value) {
      // Scroll to the bottom

      const scrollTarget = scrollAreaRef.value.getScrollTarget();
      const scrollHeight = scrollTarget.scrollHeight;
      const isAtBottom =
        scrollTarget.scrollTop + scrollTarget.clientHeight === scrollHeight;

      if (!isAtBottom) {
        scrollAreaRef.value.setScrollPosition('vertical', scrollHeight, 300);
      }
    } else {
      console.log('scrollAreaRef is not defined');
    }
  },
  { immediate: true },
);
</script>

<style scoped>
.q-scroll-area {
  height: 100%;
  width: 100%;
  contain: none !important;
}

.tip-button-container {
  max-width: 44rem;
  width: 100%;
}

.tip-button {
  border: 1px solid rgba(255, 255, 255, 0.28);
  color: rgba(255, 255, 255, 0.7);
  border-radius: 15px;
  width: 100%;
  height: 100%;
  font-size: 1.1em;
}
</style>
