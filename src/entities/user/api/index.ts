/**
 * Generates a random ID string using alphanumeric characters.
 * @param {number} length - The length of the ID to generate. Defaults to 20.
 * @returns {string} The generated random ID.
 */
export const generateRandomId = (length: number = 20): string => {
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'; // Letters + Numbers
  let result = '';
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
};
