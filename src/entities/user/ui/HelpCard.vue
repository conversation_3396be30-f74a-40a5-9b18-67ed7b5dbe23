<template>
  <q-card
    flat
    :style="
      $q.screen.lt.sm
        ? 'width: calc(100vw - 10px)'
        : 'min-width: calc(50vw - 600px)'
    "
  >
    <q-card-section class="q-pa-none" v-if="barShow">
      <q-bar class="bg-primary text-white">
        <q-icon name="img:robot.png" />

        <div>Help and Support</div>

        <q-space />
        <q-btn dense flat icon="minimize" @click="emits('minimizeToggle')">
          <q-tooltip class="bg-white text-primary">Minimize</q-tooltip>
        </q-btn>
        <q-btn dense flat icon="close" @click="emits('closeToggle')">
          <q-tooltip class="bg-white text-primary">Close</q-tooltip>
        </q-btn>
      </q-bar>
    </q-card-section>
    <q-card-section>
      <q-form id="contact-form" class="q-gutter-md" @submit.prevent="onSubmit">
        <div id="title" class="q-mb-lg">
          Reach out to the Manuscriptr Team for help!
        </div>
        <q-input
          v-model="name"
          label="Name"
          name="subject"
          required
          outlined
          :rules="[validateRequired]"
        />

        <q-input
          v-model="email"
          label="Email"
          name="email"
          type="email"
          required
          outlined
          :rules="[validateRequired, validateEmail]"
        />

        <q-input
          v-model="message"
          label="Message"
          name="body"
          type="textarea"
          required
          outlined
          :rules="[validateRequired, validateMessageLength]"
        />
      </q-form>
    </q-card-section>
    <q-separator />

    <q-card-actions align="right" class="bg-primary">
      <q-btn
        label="Send Message"
        type="submit"
        flat
        text-color="white"
        color="accent"
        icon="send"
        v-ripple
        @click="onSubmit"
        :disable="isSending || !canSendEmail"
        :loading="isSending"
      >
        <q-tooltip> Reach out to the Manuscriptr Team for help!</q-tooltip>
      </q-btn>
    </q-card-actions>
    <q-inner-loading
      :showing="isSending"
      color="primary"
      label="Hang tight, Sending Email...."
      label-class="text-primary"
      label-style="font-size: 1.1em"
      style="z-index: 1"
    >
      <q-spinner-dots color="primary" size="2em" />
      <div class="align-center">
        <q-icon name="img:robot.png" class="q-ml-xs" size="xl" />
        Hang tight, Sending Email....
      </div>
    </q-inner-loading>
  </q-card>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useQuasar } from 'quasar';
import { user } from 'src/entities/user';
import { httpsCallable } from 'firebase/functions';
import { functions } from 'src/firebase';
import { useRoute } from 'vue-router';

// Type definition for email details
type EmailDetails = {
  to: string[];
  from: string;
  subject: string;
  text: string;
  html: string;
};

// Definition of component event emitters
const emits = defineEmits<{
  minimizeToggle: [];
  closeToggle: [];
}>();

const { notify } = useQuasar(); // Quasar notification service instance
const route = useRoute(); // Current router instance
const barShow = ref(true); // State to show or hide the bar
const name = ref<string>(''); // Ref for storing the name input
const email = ref<string>(''); // Ref for storing the email input
const message = ref<string>(''); // Ref for storing the message input
const isSending = ref<boolean>(false); // State to manage sending status

/**
 * Validates if the field is required.
 * @param val - The value to validate.
 * @returns A string with an error message or true if valid.
 */
const validateRequired = (val: string): string | true =>
  val.length === 0 ? 'This field is required.' : true;

/**
 * Validates the email format.
 * @param val - The email to validate.
 * @returns A string with an error message or true if valid.
 */
const validateEmail = (val: string): string | true =>
  /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val) ? true : 'Please enter a valid email.';

/**
 * Validates the length of the message.
 * @param val - The message to validate.
 * @returns A string with an error message or true if valid.
 */
const validateMessageLength = (val: string): string | true => {
  if (val.length === 0) return 'This field is required.';
  if (val.length < 20) return 'Please enter a minimum of 20 characters.';
  if (val.length > 200) return 'Must not exceed 200 characters.';
  return true;
};

/**
 * Sends an email with the help request details.
 * @param emailDetails - The email details.
 * @returns A promise that resolves when the email is sent.
 */
const sendEmail = async (emailDetails: EmailDetails): Promise<boolean> => {
  const sendEmailFunction = httpsCallable(functions, 'sendEmail');
  return sendEmailFunction(emailDetails).then((response) => {
    console.log('Send email: ', response);
    return true;
  });
};

const canSendEmail = computed(() => {
  // Computed property to determine if the email can be sent
  return !(
    validateRequired(name.value) !== true ||
    validateRequired(email.value) !== true ||
    validateEmail(email.value) !== true ||
    validateRequired(message.value) !== true ||
    validateMessageLength(message.value) !== true
  );
});

/**
 * Handles the form submission.
 */
const onSubmit = async (): Promise<void> => {
  if (!canSendEmail.value) {
    return;
  }

  isSending.value = true;
  try {
    await sendEmail({
      to: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      from: email.value,
      subject: `New help request from ${name.value}`,
      text: `${message.value}\n\nMessage from: ${email.value}`,
      html: '',
    });
    notify({
      message: 'Message sent.',
      color: 'primary',
      timeout: 3000,
      icon: 'email',
    });

    message.value = '';
  } catch (error) {
    console.error('Failed to send message:', error);
  } finally {
    isSending.value = false;
  }
};

/**
 * Lifecycle hook that runs once the component is mounted.
 */
onMounted(() => {
  // Set name from user entity if available
  name.value = user.value?.displayName || '';
  // Set email from user entity if available
  email.value = user.value?.email || '';
  // Check if the current route is under help section
  if (route.path.startsWith('/help')) {
    // Hide the bar if on help page
    barShow.value = false;
  }
});
</script>

<style lang="scss"></style>
