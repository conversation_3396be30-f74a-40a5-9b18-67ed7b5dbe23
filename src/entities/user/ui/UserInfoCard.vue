<template>
  <q-card class="my-card" flat>
    <q-card-section vertical>
      <q-card-section class="col-12 flex flex-center">
        <q-avatar size="150px" color="primary" text-color="white">
          <q-img src="robot.png" width="150px" />
        </q-avatar>
      </q-card-section>
      <q-separator spaced />
      <q-card-section class="q-pt-xs text-center">
        <div class="text-h5 q-mt-sm q-mb-xs">
          {{ user?.displayName }}
        </div>
        <div class="text-caption text-grey">
          <span class="text-body1">{{ user?.email }}</span>
        </div>
        <div
          class="text-caption text-grey"
          v-if="
            userLoggedIn?.accountDeletionRequestedAt &&
            userLoggedIn?.accountDeletionScheduledDate
          "
        >
          <strong>Deletion Requested At: </strong
          ><span>{{
            getHumanReadableDate(userLoggedIn?.accountDeletionRequestedAt)
          }}</span>
          <br />
          <strong>Scheduled Deletion: </strong
          ><span>{{
            getHumanReadableDate(userLoggedIn?.accountDeletionScheduledDate)
          }}</span>
        </div>
        <div
          class="q-mt-sm"
          v-if="userLoggedIn && userLoggedIn.stripeId && !userLoggedIn.paypalId"
        >
          <q-btn
            color="primary"
            @click="portalLink"
            :disable="!linkToStripe"
            target="_blank"
            icon-right="fa-brands fa-stripe"
            label="Update Payment"
            v-if="userLoggedIn.stripeId"
          >
            <q-tooltip>Update Stripe Payment </q-tooltip>
          </q-btn>
        </div>
      </q-card-section>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { type User } from 'firebase/auth';
import { goToPortalLink } from 'src/entities/package';
import { userLoggedIn } from 'src/entities/user';
import { onMounted, ref } from 'vue';
import { formatDate, getHumanReadableDate } from 'src/entities/setting';

// Define component props
defineProps<{ user: User | null }>();

// Reference to store the Stripe link
const linkToStripe = ref<string>('');

/**
 * Lifecycle hook that runs once the component is mounted.
 */
onMounted(async () => {
  // additional await to ensure portal link will generate
  await new Promise((resolve) => setTimeout(resolve, 1000));
  if (userLoggedIn.value?.stripeId) {
    linkToStripe.value = await goToPortalLink(userLoggedIn.value?.stripeId);
  }
});

/**
 * Fetches and opens the portal link in a new tab.
 * @returns {Promise<void>}
 */
const portalLink = async (): Promise<void> => {
  linkToStripe.value = await goToPortalLink(userLoggedIn.value?.stripeId);
  window.open(linkToStripe.value, '_blank');
};
</script>
