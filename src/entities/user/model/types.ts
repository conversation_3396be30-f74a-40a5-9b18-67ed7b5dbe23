import { FieldValue, Unsubscribe } from '@firebase/firestore';

/**
 * Author interface definition.
 */
export interface Author {
  id?: string;
  email?: string;
  allowed_books?: number;
  accountDeletionRequestedAt?: number | null;
  accountDeletionScheduledDate?: number | null;
  name?: string;
  firstSignIn?: string | FieldValue;
  stripeId?: string;
  paypalId?: string;
  is_author?: boolean;
  assignedAdminId?: string;
  assignedAdminEmail?: string;
  features?: string[];
  unsubscribe?: Unsubscribe;
  notifications?: number;
  notifications_read?: number;
  authorsToReview?: { [key: string]: any };
}
