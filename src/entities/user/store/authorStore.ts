// authorStore.ts
import { defineStore } from 'pinia';
import { ref, Ref } from 'vue';
import {
  collection,
  doc,
  getDoc,
  getDocs,
  onSnapshot,
  query,
  setDoc,
  Unsubscribe,
  where,
} from 'firebase/firestore';
import { firestore } from 'src/firebase';
import type { Author } from '../model';

/**
 * Define a store for managing author data.
 * @returns The author store with state and actions.
 */
export const useAuthorStore = defineStore('author', () => {
  // State
  const author = ref<Author>(); // Current author
  const reviewers = ref<Author[]>([]); // List of reviewers
  const authorsForReview = ref<Author[]>([]); // Authors assigned for review
  const allAuthorsForReview = ref<Author[]>([]); // All authors available for review
  let unSubUserLoggedIn: Unsubscribe; // Unsubscribe function for user logged in listener

  /**
   * Update author data.
   * @param id - The ID of the author to update.
   * @param newData - The new data for the author.
   * @returns A promise that resolves when the update is complete.
   */
  const updateAuthor = async (
    id: string,
    newData: Partial<Author>,
  ): Promise<void> => {
    return await setDoc(doc(firestore, 'users', id), newData, { merge: true });
  };

  /**
   * Update reviewer authors_to_review data.
   * @param id - The ID of the reviewer to update.
   * @param newData - The new data for the reviewer.
   * @param isAdd - Flag to determine if newData should be added or removed.
   * @returns A promise that resolves when the update is complete.
   */
  const updateReviewerAuthorsToReview = async (
    id: string,
    newData: Partial<Author>,
    isAdd: boolean = true,
  ): Promise<void> => {
    const docRef = doc(firestore, 'users', id);
    const docSnap = await getDoc(docRef);
    const existingData = docSnap.data();
    let authorsToReview = existingData?.authorsToReview || {};

    if (isAdd) {
      if (!authorsToReview[newData.id!]) {
        authorsToReview[newData.id!] = newData;
      }
    } else {
      delete authorsToReview[newData.id!];
    }

    const updatedData = {
      ...existingData,
      authorsToReview: {
        ...authorsToReview,
      },
    };
    return await setDoc(docRef, updatedData, { merge: true });
  };

  /**
   * Update metadata for an author.
   * @param id - The ID of the author.
   * @param newData - The new metadata for the author.
   * @returns A promise that resolves when the metadata is updated.
   */
  const updateAuthorMetadata = async (
    id: string,
    newData: Partial<Author>,
  ): Promise<void> => {
    const docRefMeta = doc(firestore, `users/${id}/meta/counts`);
    return await setDoc(docRefMeta, newData, { merge: true });
  };

  /**
   * List all reviewers.
   * @returns A promise that resolves to an array of reviewers.
   */
  const listReviewers = async (): Promise<Author[]> => {
    const authorsQuery = collection(firestore, 'users');
    const reviewersList: Author[] = [];
    const authors = await getDocs(authorsQuery);
    authors.docs.forEach((doc) => {
      const author: Author = {
        id: doc.id,
        email: doc.data().email ?? '',
        name: doc.data().name ?? '',
        assignedAdminId: doc.data().assignedAdminId ?? '',
        features: doc.data().features ?? [],
      };
      reviewersList.push(author);
    });
    reviewers.value = reviewersList;
    return reviewersList;
  };

  /**
   * List authors assigned for review by a specific admin.
   * @param uid - The UID of the admin.
   * @returns A reactive reference to the list of authors.
   */
  const listAuthorsForReview = (uid: string): Ref<Author[]> => {
    const authorsQuery = query(
      collection(firestore, 'users'),
      where('assignedAdminId', '==', uid),
      where('features', 'array-contains', 'review'),
    );
    onSnapshot(authorsQuery, (snap) => {
      authorsForReview.value = snap.docs.map((doc) => doc.data() as Author);
    });
    return authorsForReview;
  };

  /**
   * List all authors available for review.
   * @returns A reactive reference to the list of all authors.
   */
  const listAllAuthorsForReview = (): Ref<Author[]> => {
    const authorsQuery = query(
      collection(firestore, 'users'),
      where('features', 'array-contains', 'review'),
    );
    onSnapshot(authorsQuery, (snap) => {
      allAuthorsForReview.value = snap.docs.map((doc) => doc.data() as Author);
    });
    return allAuthorsForReview;
  };

  /**
   * Get detailed information about an author.
   * @param uid - The UID of the author.
   * @returns A promise that resolves to the author's data.
   */
  const getAuthor = async (uid: string): Promise<Author> => {
    const docRef = doc(firestore, 'users', uid);
    const snapshot = await getDoc(docRef);
    const docRecord = snapshot.data()!;

    const record: Author = {
      id: snapshot.id,
      email: docRecord?.email ?? '',
      name: docRecord?.name ?? '',
      assignedAdminId: docRecord?.assignedAdminId ?? '',
      assignedAdminEmail: docRecord?.assignedAdminEmail ?? '',
      features: docRecord?.features ?? [],
      is_author: false,
      allowed_books: 4,
      firstSignIn: docRecord?.firstSignIn ?? '',
      stripeId: docRecord?.stripeId ?? '',
      accountDeletionRequestedAt: docRecord?.accountDeletionRequestedAt ?? '',
      accountDeletionScheduledDate:
        docRecord?.accountDeletionScheduledDate ?? '',
      paypalId: docRecord?.paypalId ?? '',
      notifications: 0,
      notifications_read: 0,
    };

    unSubUserLoggedIn = onSnapshot(docRef, (snap) => {
      const data = snap.data()!;
      record.assignedAdminId = data?.assignedAdminId ?? '';
      record.assignedAdminEmail = data?.assignedAdminEmail ?? '';
      record.features = data?.features ?? [];
      record.name = data?.name ?? '';
      record.stripeId = data?.stripeId ?? '';
      record.paypalId = data?.paypalId ?? '';
      record.accountDeletionRequestedAt =
        data?.accountDeletionRequestedAt ?? '';
      record.accountDeletionScheduledDate =
        data?.accountDeletionScheduledDate ?? '';
    });

    const docRefMeta = doc(firestore, `users/${uid}/meta/counts`);
    const snapshotMeta = await getDoc(docRefMeta);
    const docRecordMeta = snapshotMeta.data()!;
    record.is_author = docRecordMeta?.is_author ?? false;
    record.allowed_books = docRecordMeta?.allowed_books ?? 4;
    record.notifications = docRecordMeta?.notifications ?? 0;
    record.notifications_read = docRecordMeta?.notifications_read ?? 0;

    onSnapshot(docRefMeta, (snap) => {
      const data = snap.data()!;
      record.is_author = data?.is_author ?? false;
      record.allowed_books = data?.allowed_books ?? 4;
      record.notifications = data?.notifications ?? 0;
      record.notifications_read = data?.notifications_read ?? 0;
    });
    author.value = record;
    return record;
  };

  /**
   * Get author information without setting up a real-time sync.
   * @param uid - The UID of the author.
   * @returns A promise that resolves to the author's data.
   */
  const getAuthorWithoutSync = async (uid: string): Promise<Author> => {
    const docRef = doc(firestore, 'users', uid);
    const snapshot = await getDoc(docRef);
    const docRecord = snapshot.data()!;

    const author: Author = {
      id: uid,
      email: docRecord?.email ?? '',
      name: docRecord?.name ?? '',
      assignedAdminId: docRecord?.assignedAdminId ?? '',
      assignedAdminEmail: docRecord?.assignedAdminEmail ?? '',
      features: docRecord?.features ?? [],
      is_author: false,
      allowed_books: 4,
      firstSignIn: docRecord?.firstSignIn ?? '',
      stripeId: docRecord?.stripeId ?? '',
    };

    const docRefMeta = doc(firestore, `users/${uid}/meta/counts`);
    const snapshotMeta = await getDoc(docRefMeta);
    const docRecordMeta = snapshotMeta.data()!;
    author.is_author = docRecordMeta?.is_author ?? false;
    author.allowed_books = docRecordMeta?.allowed_books ?? 4;

    return author;
  };

  /**
   * List features associated with authors.
   * @returns An array of features with value and label.
   */
  const listAuthorFeatures = (): { value: string; label: string }[] => {
    return [
      { value: 'review', label: 'Review' },
      { value: 'footnote', label: 'FootNotes' },
    ];
  };

  /**
   * Update the allowed books metadata for an author.
   * @param uid - The UID of the author.
   * @param allowedBooksCount - The new count of allowed books.
   * @returns A promise that resolves when the update is complete.
   */
  const updateAuthorAllowedBooksMeta = async (
    uid: string,
    allowedBooksCount: string,
  ): Promise<void> => {
    return await setDoc(
      doc(firestore, `users/${uid}/meta/counts`),
      {
        allowed_books: parseInt(allowedBooksCount),
      },
      { merge: true },
    );
  };

  /**
   * Stop listening to changes in the user's login status.
   */
  const stopListeningUserLoggedIn = (): void => {
    if (unSubUserLoggedIn) {
      unSubUserLoggedIn();
    }
  };

  // Return state and actions
  return {
    reviewers,
    authorsForReview,
    allAuthorsForReview,
    stopListeningUserLoggedIn,
    updateAuthor,
    updateReviewerAuthorsToReview,
    updateAuthorMetadata,
    listReviewers,
    listAuthorsForReview,
    listAllAuthorsForReview,
    getAuthor,
    getAuthorWithoutSync,
    listAuthorFeatures,
    updateAuthorAllowedBooksMeta,
  };
});
